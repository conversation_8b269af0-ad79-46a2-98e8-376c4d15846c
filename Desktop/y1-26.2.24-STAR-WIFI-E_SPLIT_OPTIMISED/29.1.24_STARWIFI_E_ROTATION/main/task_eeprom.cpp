#include "task_eeprom.h"
#include "mutex_manager.h"
#include "task_display.h"
#include "task_gpio.h"

// EEPROM configuration
#define EEPROM_CHIP1_ADDR_H 0x50  // Employee LUT storage
#define EEPROM_CHIP1_ADDR_L 0x51
#define EEPROM_CHIP2_ADDR_H 0x52  // Transaction records storage
#define EEPROM_CHIP2_ADDR_L 0x53

// Memory layout constants
#define EMPLOYEE_LUT_START_ADDR 0x1000
#define TRANSACTION_START_ADDR 0x2000
#define MAX_TRANSACTIONS 10000
#define TRANSACTION_RECORD_SIZE 21  // empcode(12) + timestamp(4) + inout(1) + checksum(4)

// EEPROM state variables
static uint32_t current_write_address = TRANSACTION_START_ADDR;
static uint32_t transaction_count = 0;
static bool eeprom_initialized = false;
static bool memory_full = false;

void TaskEEPROM(void *pvParameters) {
    (void) pvParameters;
    
    DEBUG_PRINTLN("EEPROM Task started on Core 1");
    
    // Initialize EEPROM
    if (!initializeEEPROM()) {
        DEBUG_PRINTLN("ERROR: EEPROM initialization failed!");
        vTaskDelete(NULL);
        return;
    }
    
    // Load current state from EEPROM
    loadEEPROMState();
    
    CardData_t card_data;
    
    for(;;) {
        // Wait for card data to store
        if (xQueueReceive(export_data_queue, &card_data, portMAX_DELAY) == pdTRUE) {
            DEBUG_PRINT("Storing transaction: ");
            DEBUG_PRINTLN(card_data.empcode);
            
            // Check memory availability
            if (checkMemoryFull()) {
                handleMemoryFull(&card_data);
                continue;
            }
            
            // Store transaction record
            if (storeTransactionRecord(&card_data)) {
                // Update system state
                updateTransactionCounters();
                
                // Send success feedback
                sendDisplayMessage("Transaction Stored", card_data.empcode, TFT_GREEN, 2000);
                sendGPIOCommand(true, false, true, true, 1000); // OK LED + Beep + Relay
                
                DEBUG_PRINTLN("Transaction stored successfully");
            } else {
                // Storage failed
                sendDisplayMessage("Storage Failed", "Show Card Again", TFT_RED, 3000);
                sendGPIOCommand(false, true, true, false, 2000); // Error LED + Beep
                
                DEBUG_PRINTLN("ERROR: Failed to store transaction");
            }
        }
    }
}

bool initializeEEPROM(void) {
    if (!takeMutexWithTimeout(i2c_mutex, "EEPROM_INIT", I2C_TIMEOUT_MS)) {
        return false;
    }
    
    DEBUG_PRINTLN("Initializing EEPROM chips...");
    
    bool success = true;
    
    // Test EEPROM chip 1 (Employee LUT)
    if (!testEEPROMChip(EEPROM_CHIP1_ADDR_H)) {
        DEBUG_PRINTLN("ERROR: EEPROM Chip 1 not responding");
        success = false;
    }
    
    // Test EEPROM chip 2 (Transactions)
    if (!testEEPROMChip(EEPROM_CHIP2_ADDR_H)) {
        DEBUG_PRINTLN("ERROR: EEPROM Chip 2 not responding");
        success = false;
    }
    
    if (success) {
        eeprom_initialized = true;
        DEBUG_PRINTLN("EEPROM chips initialized successfully");
    }
    
    giveMutexSafe(i2c_mutex, "EEPROM_INIT");
    return success;
}

bool testEEPROMChip(uint8_t chip_address) {
    Wire.beginTransmission(chip_address);
    uint8_t error = Wire.endTransmission();
    
    if (error == 0) {
        DEBUG_PRINT("EEPROM chip 0x");
        DEBUG_PRINT(chip_address, HEX);
        DEBUG_PRINTLN(" detected");
        return true;
    } else {
        DEBUG_PRINT("EEPROM chip 0x");
        DEBUG_PRINT(chip_address, HEX);
        DEBUG_PRINT(" error: ");
        DEBUG_PRINTLN(error);
        return false;
    }
}

void loadEEPROMState(void) {
    if (!takeMutexWithTimeout(eeprom_mutex, "LOAD_STATE", 2000)) {
        DEBUG_PRINTLN("WARNING: Could not load EEPROM state");
        return;
    }
    
    DEBUG_PRINTLN("Loading EEPROM state...");
    
    // Read current write address
    current_write_address = readUint32FromEEPROM(EEPROM_CHIP2_ADDR_H, 0x00);
    if (current_write_address < TRANSACTION_START_ADDR || 
        current_write_address > (TRANSACTION_START_ADDR + (MAX_TRANSACTIONS * TRANSACTION_RECORD_SIZE))) {
        current_write_address = TRANSACTION_START_ADDR;
        DEBUG_PRINTLN("Reset write address to start");
    }
    
    // Read transaction count
    transaction_count = readUint32FromEEPROM(EEPROM_CHIP2_ADDR_H, 0x04);
    if (transaction_count > MAX_TRANSACTIONS) {
        transaction_count = 0;
        DEBUG_PRINTLN("Reset transaction count");
    }
    
    // Check if memory is full
    memory_full = (transaction_count >= MAX_TRANSACTIONS);
    
    DEBUG_PRINT("Current write address: 0x");
    DEBUG_PRINTLN(current_write_address, HEX);
    DEBUG_PRINT("Transaction count: ");
    DEBUG_PRINTLN(transaction_count);
    DEBUG_PRINT("Memory full: ");
    DEBUG_PRINTLN(memory_full ? "YES" : "NO");
    
    giveMutexSafe(eeprom_mutex, "LOAD_STATE");
}

bool storeTransactionRecord(CardData_t* card_data) {
    if (!eeprom_initialized || !card_data) {
        return false;
    }
    
    if (!takeMutexWithTimeout(eeprom_mutex, "STORE_RECORD", 2000)) {
        DEBUG_PRINTLN("ERROR: Could not acquire EEPROM mutex for storage");
        return false;
    }
    
    bool success = false;
    uint8_t record_buffer[TRANSACTION_RECORD_SIZE];
    
    // Prepare record buffer
    memset(record_buffer, 0, TRANSACTION_RECORD_SIZE);
    
    // Pack employee code (12 bytes)
    strncpy((char*)record_buffer, card_data->empcode, MAX_EMPCODE_SIZE - 1);
    
    // Pack timestamp (4 bytes)
    memcpy(&record_buffer[12], &card_data->timestamp, 4);
    
    // Pack I/O flag (1 byte)
    record_buffer[16] = card_data->inout;
    
    // Calculate and pack checksum (4 bytes)
    uint32_t checksum = calculateRecordChecksum(record_buffer, 17);
    memcpy(&record_buffer[17], &checksum, 4);
    
    // Write record to EEPROM
    if (writeEEPROMBlock(EEPROM_CHIP2_ADDR_H, current_write_address, record_buffer, TRANSACTION_RECORD_SIZE)) {
        // Verify write
        if (verifyEEPROMWrite(EEPROM_CHIP2_ADDR_H, current_write_address, record_buffer, TRANSACTION_RECORD_SIZE)) {
            success = true;
            DEBUG_PRINT("Record stored at address: 0x");
            DEBUG_PRINTLN(current_write_address, HEX);
        } else {
            DEBUG_PRINTLN("ERROR: EEPROM write verification failed");
        }
    } else {
        DEBUG_PRINTLN("ERROR: EEPROM write failed");
    }
    
    giveMutexSafe(eeprom_mutex, "STORE_RECORD");
    return success;
}

bool writeEEPROMBlock(uint8_t chip_addr, uint32_t address, uint8_t* data, uint16_t length) {
    const uint16_t page_size = 64; // Typical EEPROM page size
    uint16_t bytes_written = 0;
    
    while (bytes_written < length) {
        uint16_t bytes_to_write = min(page_size, (uint16_t)(length - bytes_written));
        uint32_t current_addr = address + bytes_written;
        
        // Start I2C transmission
        Wire.beginTransmission(chip_addr);
        
        // Send address (16-bit)
        Wire.write((current_addr >> 8) & 0xFF);
        Wire.write(current_addr & 0xFF);
        
        // Send data
        for (uint16_t i = 0; i < bytes_to_write; i++) {
            Wire.write(data[bytes_written + i]);
        }
        
        // End transmission
        uint8_t error = Wire.endTransmission();
        if (error != 0) {
            DEBUG_PRINT("I2C write error: ");
            DEBUG_PRINTLN(error);
            return false;
        }
        
        // Wait for write cycle to complete
        vTaskDelay(pdMS_TO_TICKS(10));
        
        bytes_written += bytes_to_write;
    }
    
    return true;
}

bool verifyEEPROMWrite(uint8_t chip_addr, uint32_t address, uint8_t* expected_data, uint16_t length) {
    uint8_t read_buffer[TRANSACTION_RECORD_SIZE];
    
    if (!readEEPROMBlock(chip_addr, address, read_buffer, length)) {
        return false;
    }
    
    return (memcmp(expected_data, read_buffer, length) == 0);
}

bool readEEPROMBlock(uint8_t chip_addr, uint32_t address, uint8_t* buffer, uint16_t length) {
    // Set address pointer
    Wire.beginTransmission(chip_addr);
    Wire.write((address >> 8) & 0xFF);
    Wire.write(address & 0xFF);
    uint8_t error = Wire.endTransmission(false); // Keep connection alive
    
    if (error != 0) {
        DEBUG_PRINT("I2C address set error: ");
        DEBUG_PRINTLN(error);
        return false;
    }
    
    // Read data
    uint8_t bytes_received = Wire.requestFrom(chip_addr, length);
    if (bytes_received != length) {
        DEBUG_PRINT("I2C read error: expected ");
        DEBUG_PRINT(length);
        DEBUG_PRINT(" bytes, got ");
        DEBUG_PRINTLN(bytes_received);
        return false;
    }
    
    for (uint16_t i = 0; i < length; i++) {
        buffer[i] = Wire.read();
    }
    
    return true;
}

void updateTransactionCounters(void) {
    // Update write address
    current_write_address += TRANSACTION_RECORD_SIZE;
    
    // Update transaction count
    transaction_count++;
    
    // Check for memory full condition
    if (transaction_count >= MAX_TRANSACTIONS) {
        memory_full = true;
        DEBUG_PRINTLN("WARNING: EEPROM memory is full!");
        sendDisplayMessage("Memory Full", "Export Required", TFT_RED, 5000);
    }
    
    // Save state to EEPROM
    saveEEPROMState();
}

void saveEEPROMState(void) {
    // Save current write address
    writeUint32ToEEPROM(EEPROM_CHIP2_ADDR_H, 0x00, current_write_address);
    
    // Save transaction count
    writeUint32ToEEPROM(EEPROM_CHIP2_ADDR_H, 0x04, transaction_count);
}

bool checkMemoryFull(void) {
    return memory_full;
}

void handleMemoryFull(CardData_t* card_data) {
    DEBUG_PRINTLN("Memory full - suspending card reader");
    
    // Suspend smartcard task to prevent new cards
    if (smartcard_task_handle != NULL) {
        vTaskSuspend(smartcard_task_handle);
    }
    
    // Display memory full message
    sendDisplayMessage("Memory Full", "Exporting Data...", TFT_RED, 0); // Permanent message
    
    // Wait for export to complete
    while (memory_full) {
        vTaskDelay(pdMS_TO_TICKS(1000));
        // Check if export task has cleared some space
        loadEEPROMState();
    }
    
    // Resume smartcard task
    if (smartcard_task_handle != NULL) {
        vTaskResume(smartcard_task_handle);
    }
    
    DEBUG_PRINTLN("Memory space available - resuming normal operation");
}

uint32_t calculateRecordChecksum(uint8_t* data, uint16_t length) {
    uint32_t checksum = 0;
    for (uint16_t i = 0; i < length; i++) {
        checksum += data[i];
    }
    return checksum;
}

uint32_t getCurrentWriteAddress(void) {
    return current_write_address;
}

uint32_t readUint32FromEEPROM(uint8_t chip_addr, uint32_t address) {
    uint8_t buffer[4];
    if (readEEPROMBlock(chip_addr, address, buffer, 4)) {
        return (uint32_t)buffer[0] | ((uint32_t)buffer[1] << 8) |
               ((uint32_t)buffer[2] << 16) | ((uint32_t)buffer[3] << 24);
    }
    return 0;
}

bool writeUint32ToEEPROM(uint8_t chip_addr, uint32_t address, uint32_t value) {
    uint8_t buffer[4];
    buffer[0] = value & 0xFF;
    buffer[1] = (value >> 8) & 0xFF;
    buffer[2] = (value >> 16) & 0xFF;
    buffer[3] = (value >> 24) & 0xFF;
    return writeEEPROMBlock(chip_addr, address, buffer, 4);
}

uint8_t readByteFromEEPROM(uint8_t chip_addr, uint32_t address) {
    uint8_t buffer;
    if (readEEPROMBlock(chip_addr, address, &buffer, 1)) {
        return buffer;
    }
    return 0;
}

bool writeByteToEEPROM(uint8_t chip_addr, uint32_t address, uint8_t value) {
    return writeEEPROMBlock(chip_addr, address, &value, 1);
}

bool readStringFromEEPROM(uint8_t chip_addr, uint32_t address, char* buffer, uint16_t max_length) {
    return readEEPROMBlock(chip_addr, address, (uint8_t*)buffer, max_length);
}

bool writeStringToEEPROM(uint8_t chip_addr, uint32_t address, const char* str) {
    uint16_t length = strlen(str) + 1; // Include null terminator
    return writeEEPROMBlock(chip_addr, address, (uint8_t*)str, length);
}
