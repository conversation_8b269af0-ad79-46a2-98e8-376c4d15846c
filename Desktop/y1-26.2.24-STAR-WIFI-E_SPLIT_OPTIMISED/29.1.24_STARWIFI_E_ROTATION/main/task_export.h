#ifndef TASK_EXPORT_H
#define TASK_EXPORT_H

#include "system_config.h"
#include "task_definitions.h"
#include <HTTPClient.h>
#include <ArduinoJson.h>

// Export function prototypes
void TaskExport(void *pvParameters);
void loadExportState(void);
bool hasPendingTransactions(void);
void exportTransactionBatch(void);
bool readTransactionBatch(TransactionRecord_t* records, uint8_t* count);
bool validateRecordChecksum(uint8_t* record_buffer);
void parseTransactionRecord(uint8_t* record_buffer, TransactionRecord_t* record);
void formatTimestamp(uint32_t timestamp, char* datetime_str, size_t str_size);
bool sendRecordsToWebService(TransactionRecord_t* records, uint8_t count);
void updateExportState(uint8_t exported_records);
void saveExportState(void);
void handleReExportRequest(void);
void setReExportFlag(bool flag);
bool isExportInProgress(void);

#endif