#include "task_smartcard.h"
#include "mutex_manager.h"
#include "task_display.h"

// Task handle for external control
TaskHandle_t smartcard_task_handle = NULL;

// Card reader state
static bool card_reader_initialized = false;
static uint32_t last_card_read_time = 0;
static const uint32_t CARD_DEBOUNCE_MS = 500;

void TaskSmartCard(void *pvParameters) {
    (void) pvParameters;
    
    DEBUG_PRINTLN("SmartCard Task started on Core 1");
    
    // Initialize card reader
    if (!initializeCardReader()) {
        DEBUG_PRINTLN("ERROR: Card reader initialization failed!");
        vTaskDelete(NULL);
        return;
    }
    
    // Task starts suspended - will be resumed by ISR
    vTaskSuspend(NULL);
    
    for(;;) {
        // Wait to be resumed by card detection ISR
        vTaskSuspend(NULL);
        
        DEBUG_PRINTLN("Card detected - processing...");
        
        // Process the detected card
        processDetectedCard();
        
        // Re-enable card detection
        enableCardDetection();
    }
}

bool initializeCardReader(void) {
    DEBUG_PRINTLN("Initializing card reader...");
    
    // Initialize UART for card communication
    Serial2.begin(9600, SERIAL_8N1, 16, 17); // Adjust pins as needed
    Serial2.setRxBufferSize(CARD_DATA_SIZE + 50);
    
    // Setup card detection pins
    pinMode(CARD_AVAIL_PIN, INPUT_PULLUP);
    pinMode(CARD_READ_PIN, OUTPUT);
    digitalWrite(CARD_READ_PIN, HIGH);
    
    // Setup interrupt for card detection
    attachInterrupt(digitalPinToInterrupt(CARD_AVAIL_PIN), cardDetectionISR, FALLING);
    
    card_reader_initialized = true;
    DEBUG_PRINTLN("Card reader initialized successfully");
    return true;
}

void IRAM_ATTR cardDetectionISR(void) {
    // Debounce check
    uint32_t current_time = millis();
    if ((current_time - last_card_read_time) < CARD_DEBOUNCE_MS) {
        return;
    }
    last_card_read_time = current_time;
    
    // Disable further card detection
    digitalWrite(CARD_READ_PIN, LOW);
    
    // Resume smartcard task from ISR
    BaseType_t xHigherPriorityTaskWoken = pdFALSE;
    if (smartcard_task_handle != NULL) {
        BaseType_t xHigherPriorityTaskWoken = pdFALSE;
        xTaskResumeFromISR(smartcard_task_handle);
        portYIELD_FROM_ISR(xHigherPriorityTaskWoken);
        portYIELD_FROM_ISR(xHigherPriorityTaskWoken);
    }
}

void processDetectedCard(void) {
    CardData_t card_data = {0};
    uint8_t raw_card_data[CARD_DATA_SIZE];
    
    // Read card data with timeout
    if (!readCardData(raw_card_data, CARD_READ_TIMEOUT_MS)) {
        DEBUG_PRINTLN("ERROR: Failed to read card data");
        sendDisplayMessage("Card Read Error", "", TFT_RED, 2000);
        return;
    }
    
    // Validate and parse card data
    if (!validateAndParseCard(raw_card_data, &card_data)) {
        DEBUG_PRINTLN("WARNING: Invalid card data");
        sendDisplayMessage("Invalid Card", "", TFT_YELLOW, 2000);
        return;
    }
    
    // Send card data to processing queue
    if (xQueueSend(card_data_queue, &card_data, pdMS_TO_TICKS(100)) != pdTRUE) {
        DEBUG_PRINTLN("ERROR: Failed to queue card data");
        sendDisplayMessage("System Busy", "Try Again", TFT_YELLOW, 2000);
    } else {
        DEBUG_PRINT("Card queued: ");
        DEBUG_PRINTLN(card_data.empcode);
    }
}

bool readCardData(uint8_t* buffer, uint32_t timeout_ms) {
    uint32_t start_time = millis();
    int bytes_read = 0;
    
    // Clear buffer
    memset(buffer, 0, CARD_DATA_SIZE);
    
    // Wait for data with timeout
    while ((millis() - start_time) < timeout_ms && bytes_read < CARD_DATA_SIZE) {
        if (Serial2.available()) {
            buffer[bytes_read++] = Serial2.read();
            start_time = millis(); // Reset timeout on data reception
        }
        vTaskDelay(pdMS_TO_TICKS(1));
    }
    
    if (bytes_read < CARD_DATA_SIZE) {
        DEBUG_PRINT("WARNING: Incomplete card data - got ");
        DEBUG_PRINT(bytes_read);
        DEBUG_PRINT(" bytes, expected ");
        DEBUG_PRINTLN(CARD_DATA_SIZE);
        return false;
    }
    
    return true;
}

bool validateAndParseCard(uint8_t* raw_data, CardData_t* card_data) {
    // Validate checksum
    uint32_t calculated_checksum = 0;
    uint32_t received_checksum = 0;
    
    // Calculate checksum (implement your checksum algorithm)
    for (int i = 0; i < CARD_DATA_SIZE - 4; i++) {
        calculated_checksum += raw_data[i];
    }
    
    // Extract received checksum from last 4 bytes
    memcpy(&received_checksum, &raw_data[CARD_DATA_SIZE - 4], 4);
    
    if (calculated_checksum != received_checksum) {
        DEBUG_PRINTLN("ERROR: Card checksum mismatch");
        return false;
    }
    
    // Validate header
    if (raw_data[0] != 0xBD) {
        DEBUG_PRINTLN("ERROR: Invalid card header");
        return false;
    }
    
    // Parse employee code (adjust indices based on your card format)
    memcpy(card_data->empcode, &raw_data[24], MAX_EMPCODE_SIZE - 1);
    card_data->empcode[MAX_EMPCODE_SIZE - 1] = '\0';
    
    // Parse employee name
    memcpy(card_data->empname, &raw_data[40], 15);
    card_data->empname[15] = '\0';
    
    // Parse I/O flag
    card_data->inout = raw_data[14];
    
    // Set timestamp
    card_data->timestamp = EPOCH_TIME;
    card_data->is_valid = true;
    
    return true;
}

void enableCardDetection(void) {
    digitalWrite(CARD_READ_PIN, HIGH);
    DEBUG_PRINTLN("Card detection re-enabled");
}