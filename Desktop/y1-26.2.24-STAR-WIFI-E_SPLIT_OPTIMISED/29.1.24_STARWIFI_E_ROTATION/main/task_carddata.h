#ifndef TASK_CARDDATA_H
#define TASK_CARDDATA_H

#include "system_config.h"
#include "task_definitions.h"
#include "mutex_manager.h"

// Employee lookup table structure
typedef struct {
    char empcode[MAX_EMPCODE_SIZE];
    char empname[20];
    bool is_active;
    uint32_t last_punch_time;
} EmployeeLUT_t;

// CardData function prototypes
void TaskCardData(void *pvParameters);
bool initializeEmployeeLUT(void);
void processCardData(CardData_t* card_data);
bool handleSpecialCards(CardData_t* card_data);
bool validateEmployeeCode(const char* empcode);
EmployeeLUT_t* findEmployeeInLUT(const char* empcode);
bool isDuplicatePunch(const char* employee, uint32_t timestamp);
void displayEmployeeInfo(CardData_t* card_data);
void loadEmployeeLUTFromEEPROM(void);
void enableConfigurationMode(void);
void performFactoryReset(void);
void triggerDataReExport(void);

// EEPROM helper functions
uint32_t readEmployeeCountFromEEPROM(void);
bool loadEmployeeFromEEPROM(uint32_t index, EmployeeLUT_t* employee);
void writeConfigModeFlag(bool flag);
void clearAllEEPROMData(void);
void resetSystemCounters(void);

#endif
