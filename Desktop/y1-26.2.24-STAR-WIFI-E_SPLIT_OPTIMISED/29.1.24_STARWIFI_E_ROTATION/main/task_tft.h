#ifndef TASK_TFT_H
#define TASK_TFT_H

#include "system_config.h"
#include "mutex_manager.h"
#include <TFT_eSPI.h>

// TFT Colors
#define TFT_BLACK   0x0000
#define TFT_BLUE    0x001F
#define TFT_RED     0xF800
#define TFT_GREEN   0x07E0
#define TFT_CYAN    0x07FF
#define TFT_YELLOW  0xFFE0
#define TFT_WHITE   0xFFFF
#define TFT_GRAY    0x8410

// TFT instance
extern TFT_eSPI tft;

// Display band variables
extern char band3_1[50];
extern char band3_2[50];
extern uint16_t band_color;

// TFT function prototypes
void TaskTFT(void *pvParameters);
bool initializeTFT(void);
void drawInitialDisplay(void);
void processDisplayMessage(DisplayMessage_t* msg);
void updateDisplayBand(uint8_t band_num, const char* line1, const char* line2, uint16_t color);
void updateTimeDisplay(void);
void updateNetworkStatus(void);
void handleTimedDisplay(void);
void refreshDisplayBands(void);
void formatCurrentTime(char* buffer, size_t buffer_size);
bool sendDisplayMessage(const char* line1, const char* line2, uint16_t color, uint32_t duration_ms);

#endif