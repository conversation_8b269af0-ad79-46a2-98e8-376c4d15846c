#ifndef MAIN_APPLICATION_H
#define MAIN_APPLICATION_H

#include "system_config.h"

// Main application functions
bool initializeSystem(void);
bool initializeInternalEEPROM(void);
bool loadSystemConfiguration(void);
bool loadNetworkConfiguration(void);
bool loadDeviceConfiguration(void);
bool loadTimingConfiguration(void);
bool initializeDisplaySystem(void);
bool initializeCardSystem(void);

// System status and control
void printSystemStatus(void);
void handleSystemError(const char* error_msg);
void performSystemReset(void);
bool isSystemInitialized(void);
uint32_t getSystemUptime(void);

// EEPROM addresses for internal EEPROM
#define EEPROM_IP_LEN_ADDRESS 0x10
#define EEPROM_IP_ADDRESS 0x20
#define INTERNAL_EEPROM_SIZE 512

#endif