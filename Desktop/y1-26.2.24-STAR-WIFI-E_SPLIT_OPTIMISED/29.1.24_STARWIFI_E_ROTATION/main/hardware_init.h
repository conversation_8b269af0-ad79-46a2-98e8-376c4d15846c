#ifndef HARDWARE_INIT_H
#define HARDWARE_INIT_H

#include "system_config.h"

// Hardware initialization functions
bool initializeHardware(void);
bool initializeI2C(void);
bool initializeSPI(void);
bool initializeUART(void);
bool initializePins(void);
bool testHardwareComponents(void);

// Hardware test functions
bool testI2CDevices(void);
bool testSPIDevices(void);
bool testEEPROMChips(void);

// Utility functions
void printHardwareInfo(void);
void printSystemInfo(void);

#endif