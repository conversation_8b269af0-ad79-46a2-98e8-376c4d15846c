#ifndef TASK_GPIO_H
#define TASK_GPIO_H

#include "system_config.h"
#include "mutex_manager.h"

// GPIO control functions
void TaskGPIO(void *pvParameters);
bool initializeGPIO(void);
void executeGPIOCommand(GPIOCommand_t* cmd, uint32_t* led_off_time, uint32_t* beep_off_time, uint32_t* relay_off_time);
void handleTimedGPIO(uint32_t* led_off_time, uint32_t* beep_off_time, uint32_t* relay_off_time);
bool sendGPIOCommand(bool ok_led, bool err_led, bool beep, bool relay, uint32_t duration_ms);

#endif
