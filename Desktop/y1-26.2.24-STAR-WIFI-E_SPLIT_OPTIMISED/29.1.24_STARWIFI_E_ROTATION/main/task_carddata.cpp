#include "task_carddata.h"
#include "mutex_manager.h"
#include "task_display.h"

// Employee lookup table in RAM
typedef struct {
    char empcode[MAX_EMPCODE_SIZE];
    char empname[20];
    bool is_active;
    uint32_t last_punch_time;
} EmployeeLUT_t;

static EmployeeLUT_t* employee_lut = NULL;
static uint32_t total_employees = 0;
static bool lut_loaded = false;

void TaskCardData(void *pvParameters) {
    (void) pvParameters;
    
    DEBUG_PRINTLN("CardData Task started on Core 1");
    
    // Initialize employee lookup table
    if (!initializeEmployeeLUT()) {
        DEBUG_PRINTLN("ERROR: Failed to initialize employee LUT");
        vTaskDelete(NULL);
        return;
    }
    
    CardData_t card_data;
    
    for(;;) {
        // Wait for card data from SmartCard task
        if (xQueueReceive(card_data_queue, &card_data, portMAX_DELAY) == pdTRUE) {
            DEBUG_PRINT("Processing card: ");
            DEBUG_PRINTLN(card_data.empcode);
            
            // Process the card data
            processCardData(&card_data);
        }
    }
}

bool initializeEmployeeLUT(void) {
    DEBUG_PRINTLN("Initializing Employee LUT...");
    
    // Allocate memory for LUT (adjust size as needed)
    const uint32_t max_employees = 1000;
    employee_lut = (EmployeeLUT_t*)malloc(max_employees * sizeof(EmployeeLUT_t));
    
    if (!employee_lut) {
        DEBUG_PRINTLN("ERROR: Failed to allocate memory for employee LUT");
        return false;
    }
    
    // Initialize LUT
    memset(employee_lut, 0, max_employees * sizeof(EmployeeLUT_t));
    
    // Load LUT from EEPROM
    loadEmployeeLUTFromEEPROM();
    
    DEBUG_PRINT("Employee LUT initialized with ");
    DEBUG_PRINT(total_employees);
    DEBUG_PRINTLN(" employees");
    
    return true;
}

void processCardData(CardData_t* card_data) {
    if (!card_data || !lut_loaded) {
        DEBUG_PRINTLN("ERROR: Invalid card data or LUT not loaded");
        sendDisplayMessage("System Error", "Try Again", TFT_RED, 3000);
        return;
    }
    
    // Handle special cards first
    if (handleSpecialCards(card_data)) {
        return; // Special card processed
    }
    
    // Validate employee code format
    if (!validateEmployeeCode(card_data->empcode)) {
        DEBUG_PRINTLN("WARNING: Invalid employee code format");
        sendDisplayMessage("Invalid Card", "Format Error", TFT_YELLOW, 3000);
        return;
    }
    
    // Search employee in LUT
    EmployeeLUT_t* employee = findEmployeeInLUT(card_data->empcode);
    
    if (!employee) {
        DEBUG_PRINT("WARNING: Employee not found in LUT: ");
        DEBUG_PRINTLN(card_data->empcode);
        sendDisplayMessage("Employee Not Found", card_data->empcode, TFT_YELLOW, 3000);
        return;
    }
    
    if (!employee->is_active) {
        DEBUG_PRINT("WARNING: Inactive employee: ");
        DEBUG_PRINTLN(card_data->empcode);
        sendDisplayMessage("Employee Inactive", card_data->empcode, TFT_YELLOW, 3000);
        return;
    }
    
    // Check for duplicate punch (within 30 seconds)
    if (isDuplicatePunch(employee->empcode, card_data->timestamp)) {
        DEBUG_PRINTLN("WARNING: Duplicate punch detected");
        sendDisplayMessage("Duplicate Punch", "Wait 30 seconds", TFT_YELLOW, 3000);
        return;
    }
    
    // Update employee data
    strcpy(card_data->empname, employee->empname);
    employee->last_punch_time = card_data->timestamp;
    
    // Display employee information
    displayEmployeeInfo(card_data);
    
    // Send to EEPROM storage queue
    if (xQueueSend(export_data_queue, card_data, pdMS_TO_TICKS(1000)) != pdTRUE) {
        DEBUG_PRINTLN("ERROR: Failed to queue card data for storage");
        sendDisplayMessage("Storage Error", "Show Card Again", TFT_RED, 3000);
    } else {
        DEBUG_PRINTLN("Card data queued for storage");
    }
}

bool handleSpecialCards(CardData_t* card_data) {
    char special_code = card_data->inout;
    
    switch (special_code) {
        case 'Z': // Master card - Enable configuration mode
            DEBUG_PRINTLN("Master card detected - Enabling config mode");
            sendDisplayMessage("Master Card", "Config Mode Enabled", TFT_BLUE, 5000);
            enableConfigurationMode();
            return true;
            
        case '@': // Factory reset card
            DEBUG_PRINTLN("Factory reset card detected");
            sendDisplayMessage("Factory Reset", "Resetting System...", TFT_RED, 5000);
            performFactoryReset();
            return true;
            
        case 'Y': // Re-export data card
            DEBUG_PRINTLN("Re-export card detected");
            sendDisplayMessage("Re-Export Mode", "Exporting Data...", TFT_YELLOW, 3000);
            triggerDataReExport();
            return true;
            
        default:
            return false; // Not a special card
    }
}

bool validateEmployeeCode(const char* empcode) {
    if (!empcode || strlen(empcode) == 0) {
        return false;
    }
    
    // Check length
    if (strlen(empcode) > MAX_EMPCODE_SIZE - 1) {
        return false;
    }
    
    // Check for valid characters (alphanumeric)
    for (int i = 0; empcode[i] != '\0'; i++) {
        if (!isalnum(empcode[i])) {
            return false;
        }
    }
    
    return true;
}

EmployeeLUT_t* findEmployeeInLUT(const char* empcode) {
    if (!employee_lut || !empcode) {
        return NULL;
    }
    
    // Linear search through LUT
    for (uint32_t i = 0; i < total_employees; i++) {
        if (strcmp(employee_lut[i].empcode, empcode) == 0) {
            return &employee_lut[i];
        }
    }
    
    return NULL; // Employee not found
}

bool isDuplicatePunch(EmployeeLUT_t* employee, uint32_t current_time) {
    const uint32_t duplicate_threshold = 30; // 30 seconds
    
    if (employee->last_punch_time == 0) {
        return false; // First punch
    }
    
    uint32_t time_diff = current_time - employee->last_punch_time;
    return (time_diff < duplicate_threshold);
}

void displayEmployeeInfo(CardData_t* card_data) {
    char line1[50], line2[50];
    
    // Format display message
    snprintf(line1, sizeof(line1), "EMP: %s", card_data->empcode);
    snprintf(line2, sizeof(line2), "%s | %c", card_data->empname, card_data->inout);
    
    // Send to display
    sendDisplayMessage(line1, line2, TFT_GREEN, DISPLAY_DURATION_DEFAULT);
    
    DEBUG_PRINT("Displayed employee info: ");
    DEBUG_PRINT(card_data->empcode);
    DEBUG_PRINT(" - ");
    DEBUG_PRINTLN(card_data->empname);
}

void loadEmployeeLUTFromEEPROM(void) {
    if (!takeMutexWithTimeout(eeprom_mutex, "LUT_LOAD", 5000)) {
        DEBUG_PRINTLN("ERROR: Could not load LUT - EEPROM busy");
        return;
    }
    
    DEBUG_PRINTLN("Loading employee LUT from EEPROM...");
    
    // Read total employee count from EEPROM
    total_employees = readEmployeeCountFromEEPROM();
    
    if (total_employees == 0) {
        DEBUG_PRINTLN("No employees found in EEPROM");
        lut_loaded = false;
        giveMutexSafe(eeprom_mutex, "LUT_LOAD");
        return;
    }
    
    // Load employee data
    uint32_t loaded_count = 0;
    for (uint32_t i = 0; i < total_employees && i < 1000; i++) {
        if (loadEmployeeFromEEPROM(i, &employee_lut[loaded_count])) {
            loaded_count++;
        }
    }
    
    total_employees = loaded_count;
    lut_loaded = (loaded_count > 0);
    
    DEBUG_PRINT("Loaded ");
    DEBUG_PRINT(loaded_count);
    DEBUG_PRINTLN(" employees from EEPROM");
    
    giveMutexSafe(eeprom_mutex, "LUT_LOAD");
}

void enableConfigurationMode(void) {
    // Set configuration mode flag in EEPROM
    if (takeMutexWithTimeout(eeprom_mutex, "CONFIG_MODE", 1000)) {
        writeConfigModeFlag(true);
        giveMutexSafe(eeprom_mutex, "CONFIG_MODE");
    }
    
    // Restart system to enter config mode
    vTaskDelay(pdMS_TO_TICKS(2000));
    ESP.restart();
}

void performFactoryReset(void) {
    DEBUG_PRINTLN("Performing factory reset...");
    
    if (takeMutexWithTimeout(eeprom_mutex, "FACTORY_RESET", 5000)) {
        // Clear all EEPROM data
        clearAllEEPROMData();
        
        // Reset counters and pointers
        resetSystemCounters();
        
        giveMutexSafe(eeprom_mutex, "FACTORY_RESET");
    }
    
    // Restart system
    vTaskDelay(pdMS_TO_TICKS(3000));
    ESP.restart();
}

void triggerDataReExport(void) {
    // Set re-export flag to trigger export task
    setReExportFlag(true);
    DEBUG_PRINTLN("Data re-export triggered");
}