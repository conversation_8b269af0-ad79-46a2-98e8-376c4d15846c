#include "task_import.h"
#include "mutex_manager.h"
#include "task_eeprom.h"
#include "task_display.h"
#include "task_rtc.h"

// Import configuration
#define IMPORT_RETRY_COUNT 3
#define IMPORT_RETRY_DELAY_MS 10000
#define EMPLOYEE_BATCH_SIZE 50

// Import state
static bool lut_import_required = true;
static uint32_t last_lut_update = 0;
static uint32_t lut_update_interval = 3600000; // 1 hour
static bool import_in_progress = false;

void TaskImport(void *pvParameters) {
    (void) pvParameters;
    
    DEBUG_PRINTLN("Import Task started on Core 0");
    
    // Check if LUT import is required
    checkLUTImportRequired();
    
    TickType_t xLastWakeTime = xTaskGetTickCount();
    const TickType_t xFrequency = pdMS_TO_TICKS(30000); // 30 second cycle
    
    for(;;) {
        // Check if web service is available
        if (!isWebServiceAvailable()) {
            DEBUG_PRINTLN("Web service unavailable - skipping import cycle");
            vTaskDelayUntil(&xLastWakeTime, xFrequency);
            continue;
        }
        
        // Import employee LUT if required
        if (lut_import_required || shouldUpdateLUT()) {
            DEBUG_PRINTLN("Starting LUT import...");
            import_in_progress = true;
            
            if (importEmployeeLUT()) {
                lut_import_required = false;
                last_lut_update = millis();
                sendDisplayMessage("LUT Updated", "Ready", TFT_GREEN, 3000);
            } else {
                sendDisplayMessage("LUT Import Failed", "Retrying...", TFT_RED, 3000);
            }
            
            import_in_progress = false;
        }
        
        // Import device configuration
        importDeviceConfiguration();
        
        // Sync time with server
        syncTimeWithServer();
        
        vTaskDelayUntil(&xLastWakeTime, xFrequency);
    }
}

void checkLUTImportRequired(void) {
    if (!takeMutexWithTimeout(eeprom_mutex, "CHECK_LUT", 2000)) {
        lut_import_required = true; // Assume import required if can't check
        return;
    }
    
    // Check if LUT exists in EEPROM
    uint8_t lut_flag = readByteFromEEPROM(EEPROM_CHIP1_ADDR_H, 0x00);
    uint32_t employee_count = readUint32FromEEPROM(EEPROM_CHIP1_ADDR_H, 0x01);
    
    if (lut_flag != SET_FLAG || employee_count == 0) {
        lut_import_required = true;
        DEBUG_PRINTLN("LUT import required - no valid LUT found");
    } else {
        lut_import_required = false;
        last_lut_update = readUint32FromEEPROM(EEPROM_CHIP1_ADDR_H, 0x05);
        DEBUG_PRINT("LUT found with ");
        DEBUG_PRINT(employee_count);
        DEBUG_PRINTLN(" employees");
    }
    
    giveMutexSafe(eeprom_mutex, "CHECK_LUT");
}

bool shouldUpdateLUT(void) {
    return ((millis() - last_lut_update) >= lut_update_interval);
}

bool importEmployeeLUT(void) {
    DEBUG_PRINTLN("Starting employee LUT import...");
    
    // Get total employee count from server
    uint32_t total_employees = getEmployeeCountFromServer();
    if (total_employees == 0) {
        DEBUG_PRINTLN("ERROR: No employees found on server");
        return false;
    }
    
    DEBUG_PRINT("Server has ");
    DEBUG_PRINT(total_employees);
    DEBUG_PRINTLN(" employees");
    
    // Clear existing LUT in EEPROM
    if (!clearEmployeeLUTInEEPROM()) {
        DEBUG_PRINTLN("ERROR: Failed to clear existing LUT");
        return false;
    }
    
    // Import employees in batches
    uint32_t imported_count = 0;
    uint32_t batch_start = 0;
    
    while (batch_start < total_employees) {
        uint32_t batch_size = min((uint32_t)EMPLOYEE_BATCH_SIZE, total_employees - batch_start);
        
        DEBUG_PRINT("Importing batch starting at ");
        DEBUG_PRINTLN(batch_start);
        
        if (importEmployeeBatch(batch_start, batch_size, &imported_count)) {
            batch_start += batch_size;
            
            // Update progress display
            char progress_msg[50];
            snprintf(progress_msg, sizeof(progress_msg), "%lu/%lu employees", 
                    imported_count, total_employees);
            sendDisplayMessage("Importing LUT", progress_msg, TFT_BLUE, 1000);
        } else {
            DEBUG_PRINTLN("ERROR: Failed to import employee batch");
            return false;
        }
        
        // Small delay between batches
        vTaskDelay(pdMS_TO_TICKS(100));
    }
    
    // Update LUT metadata in EEPROM
    if (updateLUTMetadata(imported_count)) {
        DEBUG_PRINT("LUT import completed - ");
        DEBUG_PRINT(imported_count);
        DEBUG_PRINTLN(" employees imported");
        return true;
    } else {
        DEBUG_PRINTLN("ERROR: Failed to update LUT metadata");
        return false;
    }
}

uint32_t getEmployeeCountFromServer(void) {
    HTTPClient http;
    uint32_t count = 0;
    
    for (int retry = 0; retry < IMPORT_RETRY_COUNT; retry++) {
        if (retry > 0) {
            vTaskDelay(pdMS_TO_TICKS(IMPORT_RETRY_DELAY_MS));
        }
        
        // Initialize HTTP client
        if (ethernet_connected) {
            EthernetClient ethClient;
            http.begin(ethClient, getEmployeeCountURL());
        } else if (wifi_connected) {
            http.begin(getEmployeeCountURL());
        } else {
            break;
        }
        
        // Set headers
        http.addHeader("Authorization", getAuthHeader());
        http.setTimeout(WEB_REQUEST_TIMEOUT_MS);
        
        // Send GET request
        int httpCode = http.GET();
        
        if (httpCode == HTTP_CODE_OK) {
            String response = http.getString();
            
            // Parse JSON response
            StaticJsonDocument<200> json_doc;
            DeserializationError error = deserializeJson(json_doc, response);
            
            if (!error) {
                count = json_doc["count"].as<uint32_t>();
                DEBUG_PRINT("Employee count from server: ");
                DEBUG_PRINTLN(count);
                break;
            } else {
                DEBUG_PRINT("JSON parse error: ");
                DEBUG_PRINTLN(error.c_str());
            }
        } else {
            DEBUG_PRINT("HTTP error: ");
            DEBUG_PRINTLN(httpCode);
        }
        
        http.end();
    }
    
    return count;
}

bool importEmployeeBatch(uint32_t start_index, uint32_t batch_size, uint32_t* imported_count) {
    HTTPClient http;
    bool success = false;
    
    // Prepare request URL with parameters
    String url = String(getEmployeeListURL()) + "?start=" + String(start_index) + 
                "&limit=" + String(batch_size);
    
    for (int retry = 0; retry < IMPORT_RETRY_COUNT; retry++) {
        if (retry > 0) {
            vTaskDelay(pdMS_TO_TICKS(IMPORT_RETRY_DELAY_MS));
        }
        
        // Initialize HTTP client
        if (ethernet_connected) {
            EthernetClient ethClient;
            http.begin(ethClient, url);
        } else if (wifi_connected) {
            http.begin(url);
        } else {
            break;
        }
        
        // Set headers
        http.addHeader("Authorization", getAuthHeader());
        http.setTimeout(WEB_REQUEST_TIMEOUT_MS);
        
        // Send GET request
        int httpCode = http.GET();
        
        if (httpCode == HTTP_CODE_OK) {
            String response = http.getString();
            
            // Parse and store employee data
            if (parseAndStoreEmployeeBatch(response, imported_count)) {
                success = true;
                break;
            }
        } else {
            DEBUG_PRINT("HTTP error: ");
            DEBUG_PRINTLN(httpCode);
        }
        
        http.end();
    }
    
    return success;
}

bool parseAndStoreEmployeeBatch(const String& json_response, uint32_t* imported_count) {
    // Parse JSON response
    DynamicJsonDocument json_doc(8192); // Larger buffer for batch data
    DeserializationError error = deserializeJson(json_doc, json_response);
    
    if (error) {
        DEBUG_PRINT("JSON parse error: ");
        DEBUG_PRINTLN(error.c_str());
        return false;
    }
    
    JsonArray employees = json_doc["employees"];
    if (employees.isNull()) {
        DEBUG_PRINTLN("ERROR: No employees array in response");
        return false;
    }
    
    if (!takeMutexWithTimeout(eeprom_mutex, "STORE_BATCH", 5000)) {
        DEBUG_PRINTLN("ERROR: Could not acquire EEPROM mutex for batch storage");
        return false;
    }
    
    bool batch_success = true;
    
    // Store each employee in EEPROM
    for (JsonObject employee : employees) {
        EmployeeRecord_t emp_record = {0};
        
        // Extract employee data
        strncpy(emp_record.empcode, employee["empcode"] | "", sizeof(emp_record.empcode) - 1);
        strncpy(emp_record.empname, employee["empname"] | "", sizeof(emp_record.empname) - 1);
        emp_record.is_active = employee["is_active"] | true;
        emp_record.department_id = employee["department_id"] | 0;
        
        // Validate employee data
        if (strlen(emp_record.empcode) == 0) {
            DEBUG_PRINTLN("WARNING: Skipping employee with empty code");
            continue;
        }
        
        // Store employee in EEPROM
        if (storeEmployeeInEEPROM(&emp_record, *imported_count)) {
            (*imported_count)++;
            
            #if DEBUG_TASKS
            if ((*imported_count % 10) == 0) {
                DEBUG_PRINT("Imported ");
                DEBUG_PRINT(*imported_count);
                DEBUG_PRINTLN(" employees");
            }
            #endif
        } else {
            DEBUG_PRINT("ERROR: Failed to store employee ");
            DEBUG_PRINTLN(emp_record.empcode);
            batch_success = false;
            break;
        }
    }
    
    giveMutexSafe(eeprom_mutex, "STORE_BATCH");
    return batch_success;
}

bool storeEmployeeInEEPROM(EmployeeRecord_t* employee, uint32_t index) {
    if (!employee) return false;
    
    // Calculate EEPROM address for this employee
    uint32_t employee_address = EMPLOYEE_LUT_START_ADDR + (index * sizeof(EmployeeRecord_t));
    
    // Write employee record to EEPROM
    return writeEEPROMBlock(EEPROM_CHIP1_ADDR_H, employee_address, 
                           (uint8_t*)employee, sizeof(EmployeeRecord_t));
}

bool updateLUTMetadata(uint32_t employee_count) {
    if (!takeMutexWithTimeout(eeprom_mutex, "LUT_META", 2000)) {
        return false;
    }
    
    bool success = true;
    
    // Write LUT flag
    if (!writeByteToEEPROM(EEPROM_CHIP1_ADDR_H, 0x00, SET_FLAG)) {
        success = false;
    }
    
    // Write employee count
    if (!writeUint32ToEEPROM(EEPROM_CHIP1_ADDR_H, 0x01, employee_count)) {
        success = false;
    }
    
    // Write last update timestamp
    if (!writeUint32ToEEPROM(EEPROM_CHIP1_ADDR_H, 0x05, millis())) {
        success = false;
    }
    
    giveMutexSafe(eeprom_mutex, "LUT_META");
    return success;
}

bool clearEmployeeLUTInEEPROM(void) {
    if (!takeMutexWithTimeout(eeprom_mutex, "CLEAR_LUT", 5000)) {
        return false;
    }
    
    DEBUG_PRINTLN("Clearing existing LUT in EEPROM...");
    
    // Clear LUT flag
    writeByteToEEPROM(EEPROM_CHIP1_ADDR_H, 0x00, 0x00);
    
    // Clear employee count
    writeUint32ToEEPROM(EEPROM_CHIP1_ADDR_H, 0x01, 0);
    
    // Clear a portion of LUT area (first few KB)
    uint32_t clear_size = 4096; // Clear first 4KB
    uint8_t zero_buffer[64] = {0};
    
    for (uint32_t addr = EMPLOYEE_LUT_START_ADDR; addr < (EMPLOYEE_LUT_START_ADDR + clear_size); addr += 64) {
        writeEEPROMBlock(EEPROM_CHIP1_ADDR_H, addr, zero_buffer, 64);
        
        // Yield to other tasks periodically
        if ((addr % 1024) == 0) {
            vTaskDelay(pdMS_TO_TICKS(10));
        }
    }
    
    giveMutexSafe(eeprom_mutex, "CLEAR_LUT");
    DEBUG_PRINTLN("LUT cleared successfully");
    return true;
}

void importDeviceConfiguration(void) {
    static uint32_t last_config_check = 0;
    const uint32_t config_check_interval = 300000; // 5 minutes
    
    if ((millis() - last_config_check) < config_check_interval) {
        return; // Too soon to check again
    }
    
    last_config_check = millis();
    
    DEBUG_PRINTLN("Checking device configuration...");
    
    HTTPClient http;
    bool success = false;
    
    // Initialize HTTP client
    if (ethernet_connected) {
        EthernetClient ethClient;
        http.begin(ethClient, getDeviceConfigURL());
    } else if (wifi_connected) {
        http.begin(getDeviceConfigURL());
    } else {
        return;
    }
    
    // Set headers
    http.addHeader("Authorization", getAuthHeader());
    http.setTimeout(WEB_REQUEST_TIMEOUT_MS);
    
    // Send GET request
    int httpCode = http.GET();
    
    if (httpCode == HTTP_CODE_OK) {
        String response = http.getString();
        
        // Parse and apply configuration
        if (parseAndApplyDeviceConfig(response)) {
            DEBUG_PRINTLN("Device configuration updated");
        }
    } else {
        DEBUG_PRINT("Config check HTTP error: ");
        DEBUG_PRINTLN(httpCode);
    }
    
    http.end();
}

bool parseAndApplyDeviceConfig(const String& json_response) {
    StaticJsonDocument<1024> json_doc;
    DeserializationError error = deserializeJson(json_doc, json_response);
    
    if (error) {
        DEBUG_PRINT("Config JSON parse error: ");
        DEBUG_PRINTLN(error.c_str());
        return false;
    }
    
    bool config_changed = false;
    
    // Update display duration if changed
    uint32_t new_display_duration = json_doc["display_duration"] | DISPLAY_DURATION_DEFAULT;
    if (new_display_duration != getDisplayDuration()) {
        setDisplayDuration(new_display_duration);
        config_changed = true;
        DEBUG_PRINT("Display duration updated to ");
        DEBUG_PRINTLN(new_display_duration);
    }
    
    // Update beep duration if changed
    uint32_t new_beep_duration = json_doc["beep_duration"] | BEEP_DURATION_DEFAULT;
    if (new_beep_duration != getBeepDuration()) {
        setBeepDuration(new_beep_duration);
        config_changed = true;
        DEBUG_PRINT("Beep duration updated to ");
        DEBUG_PRINTLN(new_beep_duration);
    }
    
    // Update device name if changed
    const char* new_device_name = json_doc["device_name"];
    if (new_device_name && strcmp(new_device_name, getDeviceName()) != 0) {
        setDeviceName(new_device_name);
        config_changed = true;
        DEBUG_PRINT("Device name updated to ");
        DEBUG_PRINTLN(new_device_name);
    }
    
    if (config_changed) {
        // Save configuration to EEPROM
        saveDeviceConfiguration();
        sendDisplayMessage("Config Updated", "Applied", TFT_GREEN, 2000);
    }
    
    return true;
}

void syncTimeWithServer(void) {
    static uint32_t last_time_sync = 0;
    const uint32_t time_sync_interval = 3600000; // 1 hour
    
    if ((millis() - last_time_sync) < time_sync_interval) {
        return; // Too soon to sync again
    }
    
    last_time_sync = millis();
    
    DEBUG_PRINTLN("Syncing time with server...");
    
    HTTPClient http;
    
    // Initialize HTTP client
    if (ethernet_connected) {
        EthernetClient ethClient;
        http.begin(ethClient, getTimeServerURL());
    } else if (wifi_connected) {
        http.begin(getTimeServerURL());
    } else {
        return;
    }
    
    // Set headers
    http.addHeader("Authorization", getAuthHeader());
    http.setTimeout(WEB_REQUEST_TIMEOUT_MS);
    
    // Send GET request
    int httpCode = http.GET();
    
    if (httpCode == HTTP_CODE_OK) {
        String response = http.getString();
        
        // Parse time response
        StaticJsonDocument<200> json_doc;
        DeserializationError error = deserializeJson(json_doc, response);
        
        if (!error) {
            uint32_t server_time = json_doc["timestamp"];
            if (server_time > 0) {
                // Update system time
                updateSystemTime(server_time);
                DEBUG_PRINTLN("Time synchronized with server");
                sendDisplayMessage("Time Synced", "Server", TFT_GREEN, 2000);
            }
        }
    } else {
        DEBUG_PRINT("Time sync HTTP error: ");
        DEBUG_PRINTLN(httpCode);
    }
    
    http.end();
}

bool isImportInProgress(void) {
    return import_in_progress;
}