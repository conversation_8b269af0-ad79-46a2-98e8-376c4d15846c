#include "hardware_init.h"
#include "mutex_manager.h"
#include <SPI.h>

bool initializeHardware(void) {
    DEBUG_PRINTLN("=== Hardware Initialization ===");
    
    // Print system information
    printSystemInfo();
    
    // Initialize I2C bus
    if (!initializeI2C()) {
        DEBUG_PRINTLN("ERROR: I2C initialization failed");
        return false;
    }
    
    // Initialize SPI bus
    if (!initializeSPI()) {
        DEBUG_PRINTLN("ERROR: SPI initialization failed");
        return false;
    }
    
    // Initialize UART
    if (!initializeUART()) {
        DEBUG_PRINTLN("ERROR: UART initialization failed");
        return false;
    }
    
    // Initialize GPIO pins
    if (!initializePins()) {
        DEBUG_PRINTLN("ERROR: Pin initialization failed");
        return false;
    }
    
    // Test hardware components
    if (!testHardwareComponents()) {
        DEBUG_PRINTLN("WARNING: Some hardware components failed tests");
        // Continue anyway - some components may be optional
    }
    
    DEBUG_PRINTLN("=== Hardware Initialization Complete ===");
    return true;
}

bool initializeI2C(void) {
    DEBUG_PRINTLN("Initializing I2C bus...");
    
    // Initialize I2C with custom pins if needed
    Wire.begin(21, 22); // SDA=21, SCL=22 (default ESP32 pins)
    Wire.setClock(100000); // 100kHz for reliable operation with EEPROM
    
    // Test I2C bus
    DEBUG_PRINTLN("Scanning I2C bus...");
    uint8_t devices_found = 0;
    
    for (uint8_t address = 1; address < 127; address++) {
        Wire.beginTransmission(address);
        uint8_t error = Wire.endTransmission();
        
        if (error == 0) {
            DEBUG_PRINT("I2C device found at address 0x");
            if (address < 16) DEBUG_PRINT("0");
            DEBUG_PRINTLN(address, HEX);
            devices_found++;
        }
    }
    
    DEBUG_PRINT("Found ");
    DEBUG_PRINT(devices_found);
    DEBUG_PRINTLN(" I2C devices");
    
    return true; // I2C initialization always succeeds
}

bool initializeSPI(void) {
    DEBUG_PRINTLN("Initializing SPI bus...");
    
    // Initialize SPI with default pins
    SPI.begin();
    SPI.setFrequency(1000000); // 1MHz for reliable operation
    
    DEBUG_PRINTLN("SPI bus initialized");
    return true;
}

bool initializeUART(void) {
    DEBUG_PRINTLN("Initializing UART interfaces...");
    
    // UART0 is already initialized for Serial (debug)
    DEBUG_PRINTLN("UART0 (Serial): 115200 baud - Debug output");
    
    // Initialize UART2 for card reader
    Serial2.begin(9600, SERIAL_8N1, 16, 17); // RX=16, TX=17
    Serial2.setRxBufferSize(512);
    DEBUG_PRINTLN("UART2 (Card Reader): 9600 baud - Card communication");
    
    return true;
}

bool initializePins(void) {
    DEBUG_PRINTLN("Initializing GPIO pins...");
    
    // Card reader control pins
    pinMode(CARD_AVAIL_PIN, INPUT_PULLUP);
    pinMode(CARD_READ_PIN, OUTPUT);
    digitalWrite(CARD_READ_PIN, HIGH);
    
    // SPI chip select pin
    pinMode(CS_PIN, OUTPUT);
    digitalWrite(CS_PIN, HIGH);
    
    DEBUG_PRINTLN("GPIO pins initialized:");
    DEBUG_PRINT("  CARD_AVAIL_PIN: ");
    DEBUG_PRINTLN(CARD_AVAIL_PIN);
    DEBUG_PRINT("  CARD_READ_PIN: ");
    DEBUG_PRINTLN(CARD_READ_PIN);
    DEBUG_PRINT("  CS_PIN: ");
    DEBUG_PRINTLN(CS_PIN);
    
    return true;
}

bool testHardwareComponents(void) {
    DEBUG_PRINTLN("Testing hardware components...");
    
    bool all_tests_passed = true;
    
    // Test I2C devices
    if (!testI2CDevices()) {
        DEBUG_PRINTLN("WARNING: I2C device tests failed");
        all_tests_passed = false;
    }
    
    // Test SPI devices
    if (!testSPIDevices()) {
        DEBUG_PRINTLN("WARNING: SPI device tests failed");
        all_tests_passed = false;
    }
    
    // Test EEPROM chips
    if (!testEEPROMChips()) {
        DEBUG_PRINTLN("WARNING: EEPROM tests failed");
        all_tests_passed = false;
    }
    
    return all_tests_passed;
}

bool testI2CDevices(void) {
    DEBUG_PRINTLN("Testing I2C devices...");
    
    bool pcf8574_found = false;
    bool rtc_found = false;
    bool eeprom_found = false;
    
    // Test PCF8574 GPIO expander
    Wire.beginTransmission(PCF8574_ADDRESS);
    if (Wire.endTransmission() == 0) {
        pcf8574_found = true;
        DEBUG_PRINTLN("  PCF8574 GPIO expander: OK");
    } else {
        DEBUG_PRINTLN("  PCF8574 GPIO expander: NOT FOUND");
    }
    
    // Test RTC chip (common addresses)
    uint8_t rtc_addresses[] = {0x68, 0x51}; // DS1307/DS3231, PCF8563
    for (uint8_t i = 0; i < 2; i++) {
        Wire.beginTransmission(rtc_addresses[i]);
        if (Wire.endTransmission() == 0) {
            rtc_found = true;
            DEBUG_PRINT("  RTC chip found at 0x");
            DEBUG_PRINTLN(rtc_addresses[i], HEX);
            break;
        }
    }
    if (!rtc_found) {
        DEBUG_PRINTLN("  RTC chip: NOT FOUND");
    }
    
    // Test EEPROM chips
    uint8_t eeprom_addresses[] = {0x50, 0x51, 0x52, 0x53};
    for (uint8_t i = 0; i < 4; i++) {
        Wire.beginTransmission(eeprom_addresses[i]);
        if (Wire.endTransmission() == 0) {
            eeprom_found = true;
            DEBUG_PRINT("  EEPROM chip found at 0x");
            DEBUG_PRINTLN(eeprom_addresses[i], HEX);
        }
    }
    if (!eeprom_found) {
        DEBUG_PRINTLN("  EEPROM chips: NOT FOUND");
    }
    
    return (pcf8574_found && rtc_found && eeprom_found);
}

bool testSPIDevices(void) {
    DEBUG_PRINTLN("Testing SPI devices...");
    
    // Test Ethernet controller (if present)
    digitalWrite(CS_PIN, LOW);
    SPI.transfer(0x00); // Dummy transfer
    digitalWrite(CS_PIN, HIGH);
    
    DEBUG_PRINTLN("  SPI bus: OK");
    return true;
}

bool testEEPROMChips(void) {
    DEBUG_PRINTLN("Testing EEPROM functionality...");
    
    bool test_passed = true;
    uint8_t test_addresses[] = {0x50, 0x52}; // Test both EEPROM chips
    
    for (uint8_t chip = 0; chip < 2; chip++) {
        uint8_t chip_addr = test_addresses[chip];
        
        // Test write/read cycle
        uint8_t test_data = 0xAA;
        uint32_t test_address = 0x1000; // Safe test address
        
        // Write test data
        Wire.beginTransmission(chip_addr);
        Wire.write((test_address >> 8) & 0xFF);
        Wire.write(test_address & 0xFF);
        Wire.write(test_data);
        uint8_t write_error = Wire.endTransmission();
        
        if (write_error != 0) {
            DEBUG_PRINT("  EEPROM 0x");
            DEBUG_PRINT(chip_addr, HEX);
            DEBUG_PRINTLN(": Write failed");
            test_passed = false;
            continue;
        }
        
        // Wait for write cycle
        delay(10);
        
        // Read test data
        Wire.beginTransmission(chip_addr);
        Wire.write((test_address >> 8) & 0xFF);
        Wire.write(test_address & 0xFF);
        Wire.endTransmission(false);
        
        Wire.requestFrom(chip_addr, (uint8_t)1);
        if (Wire.available()) {
            uint8_t read_data = Wire.read();
            if (read_data == test_data) {
                DEBUG_PRINT("  EEPROM 0x");
                DEBUG_PRINT(chip_addr, HEX);
                DEBUG_PRINTLN(": OK");
            } else {
                DEBUG_PRINT("  EEPROM 0x");
                DEBUG_PRINT(chip_addr, HEX);
                DEBUG_PRINTLN(": Data mismatch");
                test_passed = false;
            }
        } else {
            DEBUG_PRINT("  EEPROM 0x");
            DEBUG_PRINT(chip_addr, HEX);
            DEBUG_PRINTLN(": Read failed");
            test_passed = false;
        }
    }
    
    return test_passed;
}

void printSystemInfo(void) {
    DEBUG_PRINTLN("=== System Information ===");
    DEBUG_PRINT("ESP32 Chip Model: ");
    DEBUG_PRINTLN(ESP.getChipModel());
    DEBUG_PRINT("Chip Revision: ");
    DEBUG_PRINTLN(ESP.getChipRevision());
    DEBUG_PRINT("CPU Frequency: ");
    DEBUG_PRINT(ESP.getCpuFreqMHz());
    DEBUG_PRINTLN(" MHz");
    DEBUG_PRINT("Flash Size: ");
    DEBUG_PRINT(ESP.getFlashChipSize() / 1024 / 1024);
    DEBUG_PRINTLN(" MB");
    DEBUG_PRINT("Free Heap: ");
    DEBUG_PRINT(ESP.getFreeHeap());
    DEBUG_PRINTLN(" bytes");
    DEBUG_PRINT("Free PSRAM: ");
    DEBUG_PRINT(ESP.getFreePsram());
    DEBUG_PRINTLN(" bytes");
    DEBUG_PRINTLN("========================");
}

void printHardwareInfo(void) {
    DEBUG_PRINTLN("=== Hardware Configuration ===");
    DEBUG_PRINTLN("I2C Bus:");
    DEBUG_PRINTLN("  SDA: Pin 21");
    DEBUG_PRINTLN("  SCL: Pin 22");
    DEBUG_PRINTLN("  Frequency: 100kHz");
    DEBUG_PRINTLN("SPI Bus:");
    DEBUG_PRINTLN("  MOSI: Pin 23");
    DEBUG_PRINTLN("  MISO: Pin 19");
    DEBUG_PRINTLN("  SCK: Pin 18");
    DEBUG_PRINTLN("  CS: Pin 5");
    DEBUG_PRINTLN("UART2 (Card Reader):");
    DEBUG_PRINTLN("  RX: Pin 16");
    DEBUG_PRINTLN("  TX: Pin 17");
    DEBUG_PRINTLN("  Baud: 9600");
    DEBUG_PRINTLN("GPIO:");
    DEBUG_PRINT("  Card Available: Pin ");
    DEBUG_PRINTLN(CARD_AVAIL_PIN);
    DEBUG_PRINT("  Card Read Control: Pin ");
    DEBUG_PRINTLN(CARD_READ_PIN);
    DEBUG_PRINTLN("============================");
}