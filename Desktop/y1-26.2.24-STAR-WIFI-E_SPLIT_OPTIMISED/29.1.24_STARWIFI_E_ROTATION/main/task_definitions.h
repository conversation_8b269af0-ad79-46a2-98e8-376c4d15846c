#ifndef TASK_DEFINITIONS_H
#define TASK_DEFINITIONS_H

#include "system_config.h"

// Task handles for external control
extern TaskHandle_t rtc_task_handle;
extern TaskHandle_t tft_task_handle;
extern TaskHandle_t smartcard_task_handle;
extern TaskHandle_t carddata_task_handle;
extern TaskHandle_t eeprom_task_handle;
extern TaskHandle_t gpio_task_handle;
extern TaskHandle_t web_task_handle;
extern TaskHandle_t export_task_handle;
extern TaskHandle_t import_task_handle;

// Task function prototypes
void TaskRTC(void *pvParameters);
void TaskTFT(void *pvParameters);
void TaskSmartCard(void *pvParameters);
void TaskCardData(void *pvParameters);
void TaskEEPROM(void *pvParameters);
void TaskGPIO(void *pvParameters);
void TaskWeb(void *pvParameters);
void TaskExport(void *pvParameters);
void TaskImport(void *pvParameters);

// Task creation function
bool createAllTasks(void);

// Data structures
typedef struct {
    char empcode[MAX_EMPCODE_SIZE];
    char empname[20];
    char datetime[20];
    char inout;
    uint32_t timestamp;
} TransactionRecord_t;

typedef struct {
    char empcode[MAX_EMPCODE_SIZE];
    char empname[20];
    bool is_active;
    uint32_t department_id;
    uint32_t last_punch_time;
} EmployeeRecord_t;

#endif