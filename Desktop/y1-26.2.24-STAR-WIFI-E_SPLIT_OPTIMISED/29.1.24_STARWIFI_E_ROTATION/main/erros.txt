In file included from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_smartcard.h:4,
                 from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_smartcard.cpp:1:
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/system_config.h:14:14: error: cannot convert 'SPIClass' to 'uint8_t' {aka 'unsigned char'} in initialization
   14 | #define VSPI SPI
      |              ^~~
      |              |
      |              SPIClass
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/libraries/TFT_eSPI-master/TFT_eSPI.h:306:21: note: in expansion of macro 'VSPI'
  306 |     uint8_t  port = VSPI;
      |                     ^~~~
In file included from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/main_application.h:4,
                 from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/main_application.cpp:1:
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/system_config.h:14:14: error: cannot convert 'SPIClass' to 'uint8_t' {aka 'unsigned char'} in initialization
   14 | #define VSPI SPI
      |              ^~~
      |              |
      |              SPIClass
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/libraries/TFT_eSPI-master/TFT_eSPI.h:306:21: note: in expansion of macro 'VSPI'
  306 |     uint8_t  port = VSPI;
      |                     ^~~~
In file included from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_eeprom.h:4,
                 from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_eeprom.cpp:1:
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/system_config.h:14:14: error: cannot convert 'SPIClass' to 'uint8_t' {aka 'unsigned char'} in initialization
   14 | #define VSPI SPI
      |              ^~~
      |              |
      |              SPIClass
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/libraries/TFT_eSPI-master/TFT_eSPI.h:306:21: note: in expansion of macro 'VSPI'
  306 |     uint8_t  port = VSPI;
      |                     ^~~~
In file included from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.h:4,
                 from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.cpp:1:
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/system_config.h:14:14: error: cannot convert 'SPIClass' to 'uint8_t' {aka 'unsigned char'} in initialization
   14 | #define VSPI SPI
      |              ^~~
      |              |
      |              SPIClass
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/libraries/TFT_eSPI-master/TFT_eSPI.h:306:21: note: in expansion of macro 'VSPI'
  306 |     uint8_t  port = VSPI;
      |                     ^~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.cpp: In function 'void triggerDataReExport()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.cpp:278:5: error: 'setReExportFlag' was not declared in this scope
  278 |     setReExportFlag(true);
      |     ^~~~~~~~~~~~~~~
In file included from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.h:4,
                 from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:1:
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/system_config.h:14:14: error: cannot convert 'SPIClass' to 'uint8_t' {aka 'unsigned char'} in initialization
   14 | #define VSPI SPI
      |              ^~~
      |              |
      |              SPIClass
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/libraries/TFT_eSPI-master/TFT_eSPI.h:306:21: note: in expansion of macro 'VSPI'
  306 |     uint8_t  port = VSPI;
      |                     ^~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp: In function 'uint32_t getEmployeeCountFromServer()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:161:13: error: 'EthernetClient' was not declared in this scope
  161 |             EthernetClient ethClient;
      |             ^~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:162:24: error: 'ethClient' was not declared in this scope; did you mean 'Client'?
  162 |             http.begin(ethClient, getEmployeeCountURL());
      |                        ^~~~~~~~~
      |                        Client
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:162:35: error: 'getEmployeeCountURL' was not declared in this scope
  162 |             http.begin(ethClient, getEmployeeCountURL());
      |                                   ^~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:164:24: error: 'getEmployeeCountURL' was not declared in this scope
  164 |             http.begin(getEmployeeCountURL());
      |                        ^~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:170:41: error: 'getAuthHeader' was not declared in this scope
  170 |         http.addHeader("Authorization", getAuthHeader());
      |                                         ^~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp: In function 'bool importEmployeeBatch(uint32_t, uint32_t, uint32_t*)':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:208:25: error: 'getEmployeeListURL' was not declared in this scope
  208 |     String url = String(getEmployeeListURL()) + "?start=" + String(start_index) +
      |                         ^~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:218:13: error: 'EthernetClient' was not declared in this scope
  218 |             EthernetClient ethClient;
      |             ^~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:219:24: error: 'ethClient' was not declared in this scope; did you mean 'Client'?
  219 |             http.begin(ethClient, url);
      |                        ^~~~~~~~~
      |                        Client
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:227:41: error: 'getAuthHeader' was not declared in this scope
  227 |         http.addHeader("Authorization", getAuthHeader());
      |                                         ^~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp: In function 'void importDeviceConfiguration()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:400:9: error: 'EthernetClient' was not declared in this scope
  400 |         EthernetClient ethClient;
      |         ^~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:401:20: error: 'ethClient' was not declared in this scope; did you mean 'Client'?
  401 |         http.begin(ethClient, getDeviceConfigURL());
      |                    ^~~~~~~~~
      |                    Client
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:401:31: error: 'getDeviceConfigURL' was not declared in this scope
  401 |         http.begin(ethClient, getDeviceConfigURL());
      |                               ^~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:403:20: error: 'getDeviceConfigURL' was not declared in this scope
  403 |         http.begin(getDeviceConfigURL());
      |                    ^~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:409:37: error: 'getAuthHeader' was not declared in this scope
  409 |     http.addHeader("Authorization", getAuthHeader());
      |                                     ^~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp: In function 'void syncTimeWithServer()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:494:9: error: 'EthernetClient' was not declared in this scope
  494 |         EthernetClient ethClient;
      |         ^~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:495:20: error: 'ethClient' was not declared in this scope; did you mean 'Client'?
  495 |         http.begin(ethClient, getTimeServerURL());
      |                    ^~~~~~~~~
      |                    Client
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:495:31: error: 'getTimeServerURL' was not declared in this scope
  495 |         http.begin(ethClient, getTimeServerURL());
      |                               ^~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:497:20: error: 'getTimeServerURL' was not declared in this scope
  497 |         http.begin(getTimeServerURL());
      |                    ^~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:503:37: error: 'getAuthHeader' was not declared in this scope
  503 |     http.addHeader("Authorization", getAuthHeader());
      |                                     ^~~~~~~~~~~~~
In file included from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.h:4,
                 from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:1:
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/system_config.h:14:14: error: cannot convert 'SPIClass' to 'uint8_t' {aka 'unsigned char'} in initialization
   14 | #define VSPI SPI
      |              ^~~
      |              |
      |              SPIClass
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/libraries/TFT_eSPI-master/TFT_eSPI.h:306:21: note: in expansion of macro 'VSPI'
  306 |     uint8_t  port = VSPI;
      |                     ^~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp: In function 'void TaskExport(void*)':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:30:14: error: 'isWebServiceAvailable' was not declared in this scope; did you mean 'webservice_available'?
   30 |         if (!isWebServiceAvailable()) {
      |              ^~~~~~~~~~~~~~~~~~~~~
      |              webservice_available
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp: In function 'bool sendRecordsToWebService(TransactionRecord_t*, uint8_t)':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:220:36: error: 'getDeviceID' was not declared in this scope
  220 |         transaction["device_id"] = getDeviceID();
      |                                    ^~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:239:13: error: 'EthernetClient' was not declared in this scope
  239 |             EthernetClient ethClient;
      |             ^~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:240:24: error: 'ethClient' was not declared in this scope; did you mean 'Client'?
  240 |             http.begin(ethClient, getExportURL());
      |                        ^~~~~~~~~
      |                        Client
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:240:35: error: 'getExportURL' was not declared in this scope
  240 |             http.begin(ethClient, getExportURL());
      |                                   ^~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:242:24: error: 'getExportURL' was not declared in this scope
  242 |             http.begin(getExportURL());
      |                        ^~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:250:41: error: 'getAuthHeader' was not declared in this scope
  250 |         http.addHeader("Authorization", getAuthHeader());
      |                                         ^~~~~~~~~~~~~
In file included from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_tft.h:4,
                 from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_tft.cpp:1:
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/system_config.h:14:14: error: cannot convert 'SPIClass' to 'uint8_t' {aka 'unsigned char'} in initialization
   14 | #define VSPI SPI
      |              ^~~
      |              |
      |              SPIClass
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/libraries/TFT_eSPI-master/TFT_eSPI.h:306:21: note: in expansion of macro 'VSPI'
  306 |     uint8_t  port = VSPI;
      |                     ^~~~
In file included from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.h:4,
                 from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:1:
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/system_config.h:14:14: error: cannot convert 'SPIClass' to 'uint8_t' {aka 'unsigned char'} in initialization
   14 | #define VSPI SPI
      |              ^~~
      |              |
      |              SPIClass
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/libraries/TFT_eSPI-master/TFT_eSPI.h:306:21: note: in expansion of macro 'VSPI'
  306 |     uint8_t  port = VSPI;
      |                     ^~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp: In function 'void TaskWeb(void*)':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:38:9: error: 'checkWebServiceAvailability' was not declared in this scope; did you mean 'isWebServiceAvailable'?
   38 |         checkWebServiceAvailability();
      |         ^~~~~~~~~~~~~~~~~~~~~~~~~~~
      |         isWebServiceAvailable
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:41:9: error: 'handleConfigurationMode' was not declared in this scope
   41 |         handleConfigurationMode();
      |         ^~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:47:9: error: 'updateNetworkStatusDisplay' was not declared in this scope; did you mean 'updateNetworkStatus'?
   47 |         updateNetworkStatusDisplay();
      |         ^~~~~~~~~~~~~~~~~~~~~~~~~~
      |         updateNetworkStatus
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp: In function 'bool initializeNetworking()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:67:5: error: 'loadWebServiceConfig' was not declared in this scope
   67 |     loadWebServiceConfig();
      |     ^~~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp: In function 'bool testWebServiceConnection()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:221:19: error: no matching function for call to 'HTTPClient::begin(EthernetClient&, char [100])'
  221 |         http.begin(ethClient, web_service_url);
      |         ~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~
In file included from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.h:6:
/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src/HTTPClient.h:182:8: note: candidate: 'bool HTTPClient::begin(NetworkClient&, String)'
  182 |   bool begin(NetworkClient &client, String url);
      |        ^~~~~
/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src/HTTPClient.h:182:29: note:   no known conversion for argument 1 from 'EthernetClient' to 'NetworkClient&'
  182 |   bool begin(NetworkClient &client, String url);
      |              ~~~~~~~~~~~~~~~^~~~~~
/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src/HTTPClient.h:183:8: note: candidate: 'bool HTTPClient::begin(NetworkClient&, String, uint16_t, String, bool)'
  183 |   bool begin(NetworkClient &client, String host, uint16_t port, String uri = "/", bool https = false);
      |        ^~~~~
/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src/HTTPClient.h:183:8: note:   candidate expects 5 arguments, 2 provided
/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src/HTTPClient.h:186:8: note: candidate: 'bool HTTPClient::begin(String)'
  186 |   bool begin(String url);
      |        ^~~~~
/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src/HTTPClient.h:186:8: note:   candidate expects 1 argument, 2 provided
/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src/HTTPClient.h:187:8: note: candidate: 'bool HTTPClient::begin(String, uint16_t, String)'
  187 |   bool begin(String host, uint16_t port, String uri = "/");
      |        ^~~~~
/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src/HTTPClient.h:187:21: note:   no known conversion for argument 1 from 'EthernetClient' to 'String'
  187 |   bool begin(String host, uint16_t port, String uri = "/");
      |              ~~~~~~~^~~~
/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src/HTTPClient.h:189:8: note: candidate: 'bool HTTPClient::begin(String, const char*)'
  189 |   bool begin(String url, const char *CAcert);
      |        ^~~~~
/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src/HTTPClient.h:189:21: note:   no known conversion for argument 1 from 'EthernetClient' to 'String'
  189 |   bool begin(String url, const char *CAcert);
      |              ~~~~~~~^~~
/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src/HTTPClient.h:190:8: note: candidate: 'bool HTTPClient::begin(String, uint16_t, String, const char*)'
  190 |   bool begin(String host, uint16_t port, String uri, const char *CAcert);
      |        ^~~~~
/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src/HTTPClient.h:190:8: note:   candidate expects 4 arguments, 2 provided
/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src/HTTPClient.h:191:8: note: candidate: 'bool HTTPClient::begin(String, uint16_t, String, const char*, const char*, const char*)'
  191 |   bool begin(String host, uint16_t port, String uri, const char *CAcert, const char *cli_cert, const char *cli_key);
      |        ^~~~~
/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src/HTTPClient.h:191:8: note:   candidate expects 6 arguments, 2 provided
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp: In function 'void handleConfigurationMode()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:261:28: error: 'checkConfigModeFlag' was not declared in this scope
  261 |     bool should_activate = checkConfigModeFlag();
      |                            ^~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:271:9: error: 'startConfigurationWebServer' was not declared in this scope
  271 |         startConfigurationWebServer();
      |         ^~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:283:13: error: 'stopConfigurationWebServer' was not declared in this scope
  283 |             stopConfigurationWebServer();
      |             ^~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:286:13: error: 'clearConfigModeFlag' was not declared in this scope
  286 |             clearConfigModeFlag();
      |             ^~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:293:13: error: 'processConfigurationRequests' was not declared in this scope
  293 |             processConfigurationRequests();
      |             ^~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp: In function 'void handleUDPDiscovery()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:329:9: error: 'sendUDPDiscoveryResponse' was not declared in this scope
  329 |         sendUDPDiscoveryResponse(&udp);
      |         ^~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp: In function 'void sendUDPDiscoveryResponse(WiFiUDP*)':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:337:31: error: 'getDeviceName' was not declared in this scope; did you mean 'getDeviceID'?
  337 |     response["device_name"] = getDeviceName();
      |                               ^~~~~~~~~~~~~
      |                               getDeviceID
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:340:36: error: 'getFirmwareVersion' was not declared in this scope
  340 |     response["firmware_version"] = getFirmwareVersion();
      |                                    ^~~~~~~~~~~~~~~~~~
exit status 1

Compilation error: cannot convert 'SPIClass' to 'uint8_t' {aka 'unsigned char'} in initialization
