In file included from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_rtc.h:4,
                 from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_rtc.cpp:1:
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/system_config.h:64:20: error: expected unqualified-id before numeric constant
   64 | #define EPOCH_TIME 1640995200  // Example epoch time
      |                    ^~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_rtc.h:18:17: note: in expansion of macro 'EPOCH_TIME'
   18 | extern uint32_t EPOCH_TIME;
      |                 ^~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/system_config.h:64:20: error: expected unqualified-id before numeric constant
   64 | #define EPOCH_TIME 1640995200  // Example epoch time
      |                    ^~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_rtc.cpp:5:10: note: in expansion of macro 'EPOCH_TIME'
    5 | uint32_t EPOCH_TIME = 0;
      |          ^~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_rtc.cpp: In function 'void updateLocalTime()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/system_config.h:64:20: error: lvalue required as left operand of assignment
   64 | #define EPOCH_TIME 1640995200  // Example epoch time
      |                    ^~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_rtc.cpp:55:5: note: in expansion of macro 'EPOCH_TIME'
   55 |     EPOCH_TIME = getEpochTime();
      |     ^~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_rtc.cpp: In function 'void syncWithExternalRTC()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_rtc.cpp:76:9: error: 'isWebServiceAvailable' was not declared in this scope; did you mean 'webservice_available'?
   76 |     if (isWebServiceAvailable()) {
      |         ^~~~~~~~~~~~~~~~~~~~~
      |         webservice_available
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_gpio.cpp: In function 'void TaskGPIO(void*)':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_gpio.cpp:14:10: error: 'initializeGPIO' was not declared in this scope
   14 |     if (!initializeGPIO()) {
      |          ^~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_gpio.cpp:28:13: error: 'executeGPIOCommand' was not declared in this scope; did you mean 'sendGPIOCommand'?
   28 |             executeGPIOCommand(&gpio_cmd, &led_off_time, &beep_off_time, &relay_off_time);
      |             ^~~~~~~~~~~~~~~~~~
      |             sendGPIOCommand
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_gpio.cpp:32:9: error: 'handleTimedGPIO' was not declared in this scope
   32 |         handleTimedGPIO(&led_off_time, &beep_off_time, &relay_off_time);
      |         ^~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_gpio.cpp: At global scope:
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_gpio.cpp:155:6: error: ambiguating new declaration of 'bool sendGPIOCommand(bool, bool, bool, bool, uint32_t)'
  155 | bool sendGPIOCommand(bool ok_led, bool err_led, bool beep, bool relay, uint32_t duration_ms) {
      |      ^~~~~~~~~~~~~~~
In file included from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_gpio.cpp:1:
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_gpio.h:7:6: note: old declaration 'void sendGPIOCommand(bool, bool, bool, bool, uint32_t)'
    7 | void sendGPIOCommand(bool ok_led, bool err_led, bool beep, bool relay, uint32_t duration);
      |      ^~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/hardware_init.cpp:62:39: error: macro "DEBUG_PRINTLN" passed 2 arguments, but takes just 1
   62 |             DEBUG_PRINTLN(address, HEX);
      |                                       ^
In file included from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/hardware_init.h:4,
                 from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/hardware_init.cpp:1:
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/system_config.h:19:13: note: macro "DEBUG_PRINTLN" defined here
   19 |     #define DEBUG_PRINTLN(x) Serial.println(x)
      |             ^~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/hardware_init.cpp:171:48: error: macro "DEBUG_PRINTLN" passed 2 arguments, but takes just 1
  171 |             DEBUG_PRINTLN(rtc_addresses[i], HEX);
      |                                                ^
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/system_config.h:19:13: note: macro "DEBUG_PRINTLN" defined here
   19 |     #define DEBUG_PRINTLN(x) Serial.println(x)
      |             ^~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/hardware_init.cpp:186:51: error: macro "DEBUG_PRINTLN" passed 2 arguments, but takes just 1
  186 |             DEBUG_PRINTLN(eeprom_addresses[i], HEX);
      |                                                   ^
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/system_config.h:19:13: note: macro "DEBUG_PRINTLN" defined here
   19 |     #define DEBUG_PRINTLN(x) Serial.println(x)
      |             ^~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/hardware_init.cpp:230:39: error: macro "DEBUG_PRINT" passed 2 arguments, but takes just 1
  230 |             DEBUG_PRINT(chip_addr, HEX);
      |                                       ^
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/system_config.h:18:13: note: macro "DEBUG_PRINT" defined here
   18 |     #define DEBUG_PRINT(x) Serial.print(x)
      |             ^~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/hardware_init.cpp:250:43: error: macro "DEBUG_PRINT" passed 2 arguments, but takes just 1
  250 |                 DEBUG_PRINT(chip_addr, HEX);
      |                                           ^
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/system_config.h:18:13: note: macro "DEBUG_PRINT" defined here
   18 |     #define DEBUG_PRINT(x) Serial.print(x)
      |             ^~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/hardware_init.cpp:254:43: error: macro "DEBUG_PRINT" passed 2 arguments, but takes just 1
  254 |                 DEBUG_PRINT(chip_addr, HEX);
      |                                           ^
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/system_config.h:18:13: note: macro "DEBUG_PRINT" defined here
   18 |     #define DEBUG_PRINT(x) Serial.print(x)
      |             ^~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/hardware_init.cpp:260:39: error: macro "DEBUG_PRINT" passed 2 arguments, but takes just 1
  260 |             DEBUG_PRINT(chip_addr, HEX);
      |                                       ^
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/system_config.h:18:13: note: macro "DEBUG_PRINT" defined here
   18 |     #define DEBUG_PRINT(x) Serial.print(x)
      |             ^~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/hardware_init.cpp: In function 'bool initializeI2C()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/hardware_init.cpp:62:13: error: 'DEBUG_PRINTLN' was not declared in this scope
   62 |             DEBUG_PRINTLN(address, HEX);
      |             ^~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/hardware_init.cpp: In function 'bool initializeSPI()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/hardware_init.cpp:78:5: error: 'SPI' was not declared in this scope; did you mean 'FSPI'?
   78 |     SPI.begin();
      |     ^~~
      |     FSPI
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/hardware_init.cpp: In function 'bool testI2CDevices()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/hardware_init.cpp:171:13: error: 'DEBUG_PRINTLN' was not declared in this scope
  171 |             DEBUG_PRINTLN(rtc_addresses[i], HEX);
      |             ^~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/hardware_init.cpp:186:13: error: 'DEBUG_PRINTLN' was not declared in this scope
  186 |             DEBUG_PRINTLN(eeprom_addresses[i], HEX);
      |             ^~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/hardware_init.cpp: In function 'bool testSPIDevices()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/hardware_init.cpp:201:5: error: 'SPI' was not declared in this scope; did you mean 'FSPI'?
  201 |     SPI.transfer(0x00); // Dummy transfer
      |     ^~~
      |     FSPI
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/hardware_init.cpp: In function 'bool testEEPROMChips()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/hardware_init.cpp:230:13: error: 'DEBUG_PRINT' was not declared in this scope
  230 |             DEBUG_PRINT(chip_addr, HEX);
      |             ^~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/hardware_init.cpp:250:17: error: 'DEBUG_PRINT' was not declared in this scope
  250 |                 DEBUG_PRINT(chip_addr, HEX);
      |                 ^~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/hardware_init.cpp:254:17: error: 'DEBUG_PRINT' was not declared in this scope
  254 |                 DEBUG_PRINT(chip_addr, HEX);
      |                 ^~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/hardware_init.cpp:260:13: error: 'DEBUG_PRINT' was not declared in this scope
  260 |             DEBUG_PRINT(chip_addr, HEX);
      |             ^~~~~~~~~~~
In file included from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_smartcard.cpp:1:
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_smartcard.h:12:46: error: 'CardData_t' has not been declared
   12 | bool validateAndParseCard(uint8_t* raw_data, CardData_t* card_data);
      |                                              ^~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_smartcard.cpp: In function 'void cardDetectionISR()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_smartcard.cpp:75:9: error: 'vTaskResumeFromISR' was not declared in this scope; did you mean 'xTaskResumeFromISR'?
   75 |         vTaskResumeFromISR(smartcard_task_handle);
      |         ^~~~~~~~~~~~~~~~~~
      |         xTaskResumeFromISR
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_smartcard.cpp: In function 'void processDetectedCard()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_smartcard.cpp:87:51: error: 'TFT_RED' was not declared in this scope
   87 |         sendDisplayMessage("Card Read Error", "", TFT_RED, 2000);
      |                                                   ^~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_smartcard.cpp:87:9: error: 'sendDisplayMessage' was not declared in this scope; did you mean 'DisplayMessage_t'?
   87 |         sendDisplayMessage("Card Read Error", "", TFT_RED, 2000);
      |         ^~~~~~~~~~~~~~~~~~
      |         DisplayMessage_t
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_smartcard.cpp:92:46: error: cannot convert 'CardData_t*' to 'int*'
   92 |     if (!validateAndParseCard(raw_card_data, &card_data)) {
      |                                              ^~~~~~~~~~
      |                                              |
      |                                              CardData_t*
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_smartcard.h:12:58: note:   initializing argument 2 of 'bool validateAndParseCard(uint8_t*, int*)'
   12 | bool validateAndParseCard(uint8_t* raw_data, CardData_t* card_data);
      |                                              ~~~~~~~~~~~~^~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_smartcard.cpp:94:48: error: 'TFT_YELLOW' was not declared in this scope
   94 |         sendDisplayMessage("Invalid Card", "", TFT_YELLOW, 2000);
      |                                                ^~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_smartcard.cpp:94:9: error: 'sendDisplayMessage' was not declared in this scope; did you mean 'DisplayMessage_t'?
   94 |         sendDisplayMessage("Invalid Card", "", TFT_YELLOW, 2000);
      |         ^~~~~~~~~~~~~~~~~~
      |         DisplayMessage_t
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_smartcard.cpp:101:56: error: 'TFT_YELLOW' was not declared in this scope
  101 |         sendDisplayMessage("System Busy", "Try Again", TFT_YELLOW, 2000);
      |                                                        ^~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_smartcard.cpp:101:9: error: 'sendDisplayMessage' was not declared in this scope; did you mean 'DisplayMessage_t'?
  101 |         sendDisplayMessage("System Busy", "Try Again", TFT_YELLOW, 2000);
      |         ^~~~~~~~~~~~~~~~~~
      |         DisplayMessage_t
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_eeprom.cpp:108:38: error: macro "DEBUG_PRINT" passed 2 arguments, but takes just 1
  108 |         DEBUG_PRINT(chip_address, HEX);
      |                                      ^
In file included from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_eeprom.h:4,
                 from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_eeprom.cpp:1:
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/system_config.h:18:13: note: macro "DEBUG_PRINT" defined here
   18 |     #define DEBUG_PRINT(x) Serial.print(x)
      |             ^~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_eeprom.cpp:113:38: error: macro "DEBUG_PRINT" passed 2 arguments, but takes just 1
  113 |         DEBUG_PRINT(chip_address, HEX);
      |                                      ^
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/system_config.h:18:13: note: macro "DEBUG_PRINT" defined here
   18 |     #define DEBUG_PRINT(x) Serial.print(x)
      |             ^~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_eeprom.cpp:147:45: error: macro "DEBUG_PRINTLN" passed 2 arguments, but takes just 1
  147 |     DEBUG_PRINTLN(current_write_address, HEX);
      |                                             ^
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/system_config.h:19:13: note: macro "DEBUG_PRINTLN" defined here
   19 |     #define DEBUG_PRINTLN(x) Serial.println(x)
      |             ^~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_eeprom.cpp:191:53: error: macro "DEBUG_PRINTLN" passed 2 arguments, but takes just 1
  191 |             DEBUG_PRINTLN(current_write_address, HEX);
      |                                                     ^
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/system_config.h:19:13: note: macro "DEBUG_PRINTLN" defined here
   19 |     #define DEBUG_PRINTLN(x) Serial.println(x)
      |             ^~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_eeprom.h:18:29: error: 'CardData_t' was not declared in this scope
   18 | bool storeTransactionRecord(CardData_t* card_data);
      |                             ^~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_eeprom.h:18:41: error: 'card_data' was not declared in this scope
   18 | bool storeTransactionRecord(CardData_t* card_data);
      |                                         ^~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_eeprom.h:25:6: error: variable or field 'handleMemoryFull' declared void
   25 | void handleMemoryFull(CardData_t* card_data);
      |      ^~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_eeprom.h:25:23: error: 'CardData_t' was not declared in this scope
   25 | void handleMemoryFull(CardData_t* card_data);
      |                       ^~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_eeprom.h:25:35: error: 'card_data' was not declared in this scope
   25 | void handleMemoryFull(CardData_t* card_data);
      |                                   ^~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_eeprom.cpp: In function 'void TaskEEPROM(void*)':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_eeprom.cpp:47:17: error: 'handleMemoryFull' was not declared in this scope
   47 |                 handleMemoryFull(&card_data);
      |                 ^~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_eeprom.cpp:52:39: error: 'storeTransactionRecord' cannot be used as a function
   52 |             if (storeTransactionRecord(&card_data)) {
      |                 ~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_eeprom.cpp:57:77: error: 'TFT_GREEN' was not declared in this scope
   57 |                 sendDisplayMessage("Transaction Stored", card_data.empcode, TFT_GREEN, 2000);
      |                                                                             ^~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_eeprom.cpp:57:17: error: 'sendDisplayMessage' was not declared in this scope; did you mean 'DisplayMessage_t'?
   57 |                 sendDisplayMessage("Transaction Stored", card_data.empcode, TFT_GREEN, 2000);
      |                 ^~~~~~~~~~~~~~~~~~
      |                 DisplayMessage_t
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_eeprom.cpp:58:17: error: 'sendGPIOCommand' was not declared in this scope
   58 |                 sendGPIOCommand(true, false, true, true, 1000); // OK LED + Beep + Relay
      |                 ^~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_eeprom.cpp:63:73: error: 'TFT_RED' was not declared in this scope
   63 |                 sendDisplayMessage("Storage Failed", "Show Card Again", TFT_RED, 3000);
      |                                                                         ^~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_eeprom.cpp:63:17: error: 'sendDisplayMessage' was not declared in this scope; did you mean 'DisplayMessage_t'?
   63 |                 sendDisplayMessage("Storage Failed", "Show Card Again", TFT_RED, 3000);
      |                 ^~~~~~~~~~~~~~~~~~
      |                 DisplayMessage_t
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_eeprom.cpp:64:17: error: 'sendGPIOCommand' was not declared in this scope
   64 |                 sendGPIOCommand(false, true, true, false, 2000); // Error LED + Beep
      |                 ^~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_eeprom.cpp: In function 'bool testEEPROMChip(uint8_t)':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_eeprom.cpp:108:9: error: 'DEBUG_PRINT' was not declared in this scope
  108 |         DEBUG_PRINT(chip_address, HEX);
      |         ^~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_eeprom.cpp:113:9: error: 'DEBUG_PRINT' was not declared in this scope
  113 |         DEBUG_PRINT(chip_address, HEX);
      |         ^~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_eeprom.cpp: In function 'void loadEEPROMState()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_eeprom.cpp:147:5: error: 'DEBUG_PRINTLN' was not declared in this scope
  147 |     DEBUG_PRINTLN(current_write_address, HEX);
      |     ^~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_eeprom.cpp: At global scope:
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_eeprom.cpp:156:50: error: 'bool storeTransactionRecord(CardData_t*)' redeclared as different kind of entity
  156 | bool storeTransactionRecord(CardData_t* card_data) {
      |                                                  ^
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_eeprom.h:18:6: note: previous declaration 'bool storeTransactionRecord'
   18 | bool storeTransactionRecord(CardData_t* card_data);
      |      ^~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_eeprom.cpp: In function 'bool storeTransactionRecord(CardData_t*)':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_eeprom.cpp:191:13: error: 'DEBUG_PRINTLN' was not declared in this scope
  191 |             DEBUG_PRINTLN(current_write_address, HEX);
      |             ^~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_eeprom.cpp: In function 'bool writeEEPROMBlock(uint8_t, uint32_t, uint8_t*, uint16_t)':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_eeprom.cpp:208:38: error: no matching function for call to 'min(const uint16_t&, int)'
  208 |         uint16_t bytes_to_write = min(page_size, length - bytes_written);
      |                                   ~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
In file included from /home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/xtensa-esp-elf/include/c++/14.2.0/algorithm:61,
                 from /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/Arduino.h:191,
                 from /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src/NetworkInterface.h:10,
                 from /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src/Network.h:8,
                 from /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src/WiFiGeneric.h:44,
                 from /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src/WiFiSTA.h:30,
                 from /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src/WiFi.h:34,
                 from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/system_config.h:5:
/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/xtensa-esp-elf/include/c++/14.2.0/bits/stl_algo.h:5695:5: note: candidate: 'template<class _Tp, class _Compare> constexpr _Tp std::min(initializer_list<_Tp>, _Compare)'
 5695 |     min(initializer_list<_Tp> __l, _Compare __comp)
      |     ^~~
/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/xtensa-esp-elf/include/c++/14.2.0/bits/stl_algo.h:5695:5: note:   template argument deduction/substitution failed:
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_eeprom.cpp:208:38: note:   mismatched types 'std::initializer_list<_Tp>' and 'short unsigned int'
  208 |         uint16_t bytes_to_write = min(page_size, length - bytes_written);
      |                                   ~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/xtensa-esp-elf/include/c++/14.2.0/bits/stl_algo.h:5685:5: note: candidate: 'template<class _Tp> constexpr _Tp std::min(initializer_list<_Tp>)'
 5685 |     min(initializer_list<_Tp> __l)
      |     ^~~
/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/xtensa-esp-elf/include/c++/14.2.0/bits/stl_algo.h:5685:5: note:   candidate expects 1 argument, 2 provided
In file included from /home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/xtensa-esp-elf/include/c++/14.2.0/bits/specfun.h:43,
                 from /home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/xtensa-esp-elf/include/c++/14.2.0/cmath:3898,
                 from /home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/xtensa-esp-elf/include/c++/14.2.0/math.h:36,
                 from /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal.h:30,
                 from /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src/Wire.h:33,
                 from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/system_config.h:4:
/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/xtensa-esp-elf/include/c++/14.2.0/bits/stl_algobase.h:281:5: note: candidate: 'template<class _Tp, class _Compare> constexpr const _Tp& std::min(const _Tp&, const _Tp&, _Compare)'
  281 |     min(const _Tp& __a, const _Tp& __b, _Compare __comp)
      |     ^~~
/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/xtensa-esp-elf/include/c++/14.2.0/bits/stl_algobase.h:281:5: note:   candidate expects 3 arguments, 2 provided
/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/xtensa-esp-elf/include/c++/14.2.0/bits/stl_algobase.h:233:5: note: candidate: 'template<class _Tp> constexpr const _Tp& std::min(const _Tp&, const _Tp&)'
  233 |     min(const _Tp& __a, const _Tp& __b)
      |     ^~~
/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/xtensa-esp-elf/include/c++/14.2.0/bits/stl_algobase.h:233:5: note:   template argument deduction/substitution failed:
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_eeprom.cpp:208:38: note:   deduced conflicting types for parameter 'const _Tp' ('short unsigned int' and 'int')
  208 |         uint16_t bytes_to_write = min(page_size, length - bytes_written);
      |                                   ~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_eeprom.cpp: In function 'void updateTransactionCounters()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_eeprom.cpp:291:62: error: 'TFT_RED' was not declared in this scope
  291 |         sendDisplayMessage("Memory Full", "Export Required", TFT_RED, 5000);
      |                                                              ^~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_eeprom.cpp:291:9: error: 'sendDisplayMessage' was not declared in this scope; did you mean 'DisplayMessage_t'?
  291 |         sendDisplayMessage("Memory Full", "Export Required", TFT_RED, 5000);
      |         ^~~~~~~~~~~~~~~~~~
      |         DisplayMessage_t
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_eeprom.cpp: In function 'void handleMemoryFull(CardData_t*)':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_eeprom.cpp:319:60: error: 'TFT_RED' was not declared in this scope
  319 |     sendDisplayMessage("Memory Full", "Exporting Data...", TFT_RED, 0); // Permanent message
      |                                                            ^~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_eeprom.cpp:319:5: error: 'sendDisplayMessage' was not declared in this scope; did you mean 'DisplayMessage_t'?
  319 |     sendDisplayMessage("Memory Full", "Exporting Data...", TFT_RED, 0); // Permanent message
      |     ^~~~~~~~~~~~~~~~~~
      |     DisplayMessage_t
In file included from /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/freertos/esp_additions/include/freertos/idf_additions.h:21,
                 from /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/freertos/FreeRTOS-Kernel/include/freertos/FreeRTOS.h:1533,
                 from /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal.h:34,
                 from /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src/Wire.h:33,
                 from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/system_config.h:4,
                 from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/main_application.h:4,
                 from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/main_application.cpp:1:
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/main_application.cpp: In function 'bool initializeDisplaySystem()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/main_application.cpp:179:34: error: 'DISPLAY_QUEUE_SIZE' was not declared in this scope; did you mean 'DISPLAY_BUFFER_SIZE'?
  179 |     display_queue = xQueueCreate(DISPLAY_QUEUE_SIZE, sizeof(DisplayMessage_t));
      |                                  ^~~~~~~~~~~~~~~~~~
/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/freertos/FreeRTOS-Kernel/include/freertos/queue.h:149:81: note: in definition of macro 'xQueueCreate'
  149 |     #define xQueueCreate( uxQueueLength, uxItemSize )    xQueueGenericCreate( ( uxQueueLength ), ( uxItemSize ), ( queueQUEUE_TYPE_BASE ) )
      |                                                                                 ^~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/main_application.cpp:186:12: error: 'band3_1' was not declared in this scope
  186 |     memset(band3_1, 0, sizeof(band3_1));
      |            ^~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/main_application.cpp:187:12: error: 'band3_2' was not declared in this scope
  187 |     memset(band3_2, 0, sizeof(band3_2));
      |            ^~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/main_application.cpp:188:5: error: 'band_color' was not declared in this scope
  188 |     band_color = TFT_WHITE;
      |     ^~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/main_application.cpp:188:18: error: 'TFT_WHITE' was not declared in this scope
  188 |     band_color = TFT_WHITE;
      |                  ^~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/main_application.cpp: In function 'bool initializeCardSystem()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/main_application.cpp:198:5: error: 'card_queue' was not declared in this scope
  198 |     card_queue = xQueueCreate(CARD_QUEUE_SIZE, sizeof(CardData_t));
      |     ^~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/main_application.cpp:198:31: error: 'CARD_QUEUE_SIZE' was not declared in this scope; did you mean 'CARD_DATA_SIZE'?
  198 |     card_queue = xQueueCreate(CARD_QUEUE_SIZE, sizeof(CardData_t));
      |                               ^~~~~~~~~~~~~~~
/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/freertos/FreeRTOS-Kernel/include/freertos/queue.h:149:81: note: in definition of macro 'xQueueCreate'
  149 |     #define xQueueCreate( uxQueueLength, uxItemSize )    xQueueGenericCreate( ( uxQueueLength ), ( uxItemSize ), ( queueQUEUE_TYPE_BASE ) )
      |                                                                                 ^~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/main_application.cpp:205:5: error: 'gpio_queue' was not declared in this scope; did you mean 'sigqueue'?
  205 |     gpio_queue = xQueueCreate(GPIO_QUEUE_SIZE, sizeof(GPIOCommand_t));
      |     ^~~~~~~~~~
      |     sigqueue
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/main_application.cpp:205:31: error: 'GPIO_QUEUE_SIZE' was not declared in this scope
  205 |     gpio_queue = xQueueCreate(GPIO_QUEUE_SIZE, sizeof(GPIOCommand_t));
      |                               ^~~~~~~~~~~~~~~
/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/freertos/FreeRTOS-Kernel/include/freertos/queue.h:149:81: note: in definition of macro 'xQueueCreate'
  149 |     #define xQueueCreate( uxQueueLength, uxItemSize )    xQueueGenericCreate( ( uxQueueLength ), ( uxItemSize ), ( queueQUEUE_TYPE_BASE ) )
      |                                                                                 ^~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/main_application.cpp:213:60: error: 'cardDetectionISR' was not declared in this scope
  213 |     attachInterrupt(digitalPinToInterrupt(CARD_AVAIL_PIN), cardDetectionISR, FALLING);
      |                                                            ^~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/main_application.cpp: In function 'void handleSystemError(const char*)':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/main_application.cpp:274:51: error: 'TFT_RED' was not declared in this scope
  274 |     sendDisplayMessage("SYSTEM ERROR", error_msg, TFT_RED, 10000);
      |                                                   ^~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/main_application.cpp:274:5: error: 'sendDisplayMessage' was not declared in this scope; did you mean 'DisplayMessage_t'?
  274 |     sendDisplayMessage("SYSTEM ERROR", error_msg, TFT_RED, 10000);
      |     ^~~~~~~~~~~~~~~~~~
      |     DisplayMessage_t
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/main_application.cpp: In function 'void performSystemReset()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/main_application.cpp:284:57: error: 'TFT_YELLOW' was not declared in this scope
  284 |     sendDisplayMessage("SYSTEM RESET", "Restarting...", TFT_YELLOW, 3000);
      |                                                         ^~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/main_application.cpp:284:5: error: 'sendDisplayMessage' was not declared in this scope; did you mean 'DisplayMessage_t'?
  284 |     sendDisplayMessage("SYSTEM RESET", "Restarting...", TFT_YELLOW, 3000);
      |     ^~~~~~~~~~~~~~~~~~
      |     DisplayMessage_t
In file included from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.cpp:1:
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.h:10:6: error: variable or field 'processCardData' declared void
   10 | void processCardData(CardData_t* card_data);
      |      ^~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.h:10:22: error: 'CardData_t' was not declared in this scope
   10 | void processCardData(CardData_t* card_data);
      |                      ^~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.h:10:34: error: 'card_data' was not declared in this scope
   10 | void processCardData(CardData_t* card_data);
      |                                  ^~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.h:11:25: error: 'CardData_t' was not declared in this scope
   11 | bool handleSpecialCards(CardData_t* card_data);
      |                         ^~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.h:11:37: error: 'card_data' was not declared in this scope
   11 | bool handleSpecialCards(CardData_t* card_data);
      |                                     ^~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.h:13:1: error: 'EmployeeLUT_t' does not name a type; did you mean 'EmployeeRecord_t'?
   13 | EmployeeLUT_t* findEmployeeInLUT(const char* empcode);
      | ^~~~~~~~~~~~~
      | EmployeeRecord_t
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.h:15:6: error: variable or field 'displayEmployeeInfo' declared void
   15 | void displayEmployeeInfo(CardData_t* card_data);
      |      ^~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.h:15:26: error: 'CardData_t' was not declared in this scope
   15 | void displayEmployeeInfo(CardData_t* card_data);
      |                          ^~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.h:15:38: error: 'card_data' was not declared in this scope
   15 | void displayEmployeeInfo(CardData_t* card_data);
      |                                      ^~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.h:23:45: error: 'EmployeeLUT_t' has not been declared
   23 | bool loadEmployeeFromEEPROM(uint32_t index, EmployeeLUT_t* employee);
      |                                             ^~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.cpp: In function 'void TaskCardData(void*)':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.cpp:37:13: error: 'processCardData' was not declared in this scope
   37 |             processCardData(&card_data);
      |             ^~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.cpp: In function 'void processCardData(CardData_t*)':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.cpp:70:57: error: 'TFT_RED' was not declared in this scope
   70 |         sendDisplayMessage("System Error", "Try Again", TFT_RED, 3000);
      |                                                         ^~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.cpp:70:9: error: 'sendDisplayMessage' was not declared in this scope; did you mean 'DisplayMessage_t'?
   70 |         sendDisplayMessage("System Error", "Try Again", TFT_RED, 3000);
      |         ^~~~~~~~~~~~~~~~~~
      |         DisplayMessage_t
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.cpp:75:27: error: 'handleSpecialCards' cannot be used as a function
   75 |     if (handleSpecialCards(card_data)) {
      |         ~~~~~~~~~~~~~~~~~~^~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.cpp:82:60: error: 'TFT_YELLOW' was not declared in this scope
   82 |         sendDisplayMessage("Invalid Card", "Format Error", TFT_YELLOW, 3000);
      |                                                            ^~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.cpp:82:9: error: 'sendDisplayMessage' was not declared in this scope; did you mean 'DisplayMessage_t'?
   82 |         sendDisplayMessage("Invalid Card", "Format Error", TFT_YELLOW, 3000);
      |         ^~~~~~~~~~~~~~~~~~
      |         DisplayMessage_t
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.cpp:87:31: error: 'findEmployeeInLUT' was not declared in this scope
   87 |     EmployeeLUT_t* employee = findEmployeeInLUT(card_data->empcode);
      |                               ^~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.cpp:92:70: error: 'TFT_YELLOW' was not declared in this scope
   92 |         sendDisplayMessage("Employee Not Found", card_data->empcode, TFT_YELLOW, 3000);
      |                                                                      ^~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.cpp:92:9: error: 'sendDisplayMessage' was not declared in this scope; did you mean 'DisplayMessage_t'?
   92 |         sendDisplayMessage("Employee Not Found", card_data->empcode, TFT_YELLOW, 3000);
      |         ^~~~~~~~~~~~~~~~~~
      |         DisplayMessage_t
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.cpp:99:69: error: 'TFT_YELLOW' was not declared in this scope
   99 |         sendDisplayMessage("Employee Inactive", card_data->empcode, TFT_YELLOW, 3000);
      |                                                                     ^~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.cpp:99:9: error: 'sendDisplayMessage' was not declared in this scope; did you mean 'DisplayMessage_t'?
   99 |         sendDisplayMessage("Employee Inactive", card_data->empcode, TFT_YELLOW, 3000);
      |         ^~~~~~~~~~~~~~~~~~
      |         DisplayMessage_t
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.cpp:104:26: error: cannot convert 'EmployeeLUT_t*' to 'const char*'
  104 |     if (isDuplicatePunch(employee, card_data->timestamp)) {
      |                          ^~~~~~~~
      |                          |
      |                          EmployeeLUT_t*
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.h:14:35: note:   initializing argument 1 of 'bool isDuplicatePunch(const char*, uint32_t)'
   14 | bool isDuplicatePunch(const char* employee, uint32_t timestamp);
      |                       ~~~~~~~~~~~~^~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.cpp:106:66: error: 'TFT_YELLOW' was not declared in this scope
  106 |         sendDisplayMessage("Duplicate Punch", "Wait 30 seconds", TFT_YELLOW, 3000);
      |                                                                  ^~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.cpp:106:9: error: 'sendDisplayMessage' was not declared in this scope; did you mean 'DisplayMessage_t'?
  106 |         sendDisplayMessage("Duplicate Punch", "Wait 30 seconds", TFT_YELLOW, 3000);
      |         ^~~~~~~~~~~~~~~~~~
      |         DisplayMessage_t
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.cpp:115:5: error: 'displayEmployeeInfo' was not declared in this scope
  115 |     displayEmployeeInfo(card_data);
      |     ^~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.cpp:120:64: error: 'TFT_RED' was not declared in this scope
  120 |         sendDisplayMessage("Storage Error", "Show Card Again", TFT_RED, 3000);
      |                                                                ^~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.cpp:120:9: error: 'sendDisplayMessage' was not declared in this scope; did you mean 'DisplayMessage_t'?
  120 |         sendDisplayMessage("Storage Error", "Show Card Again", TFT_RED, 3000);
      |         ^~~~~~~~~~~~~~~~~~
      |         DisplayMessage_t
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.cpp: At global scope:
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.cpp:126:46: error: 'bool handleSpecialCards(CardData_t*)' redeclared as different kind of entity
  126 | bool handleSpecialCards(CardData_t* card_data) {
      |                                              ^
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.h:11:6: note: previous declaration 'bool handleSpecialCards'
   11 | bool handleSpecialCards(CardData_t* card_data);
      |      ^~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.cpp: In function 'bool handleSpecialCards(CardData_t*)':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.cpp:132:70: error: 'TFT_BLUE' was not declared in this scope
  132 |             sendDisplayMessage("Master Card", "Config Mode Enabled", TFT_BLUE, 5000);
      |                                                                      ^~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.cpp:132:13: error: 'sendDisplayMessage' was not declared in this scope; did you mean 'DisplayMessage_t'?
  132 |             sendDisplayMessage("Master Card", "Config Mode Enabled", TFT_BLUE, 5000);
      |             ^~~~~~~~~~~~~~~~~~
      |             DisplayMessage_t
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.cpp:138:72: error: 'TFT_RED' was not declared in this scope
  138 |             sendDisplayMessage("Factory Reset", "Resetting System...", TFT_RED, 5000);
      |                                                                        ^~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.cpp:144:71: error: 'TFT_YELLOW' was not declared in this scope
  144 |             sendDisplayMessage("Re-Export Mode", "Exporting Data...", TFT_YELLOW, 3000);
      |                                                                       ^~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.cpp: In function 'void displayEmployeeInfo(CardData_t*)':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.cpp:207:38: error: 'TFT_GREEN' was not declared in this scope
  207 |     sendDisplayMessage(line1, line2, TFT_GREEN, DISPLAY_DURATION_DEFAULT);
      |                                      ^~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.cpp:207:5: error: 'sendDisplayMessage' was not declared in this scope; did you mean 'DisplayMessage_t'?
  207 |     sendDisplayMessage(line1, line2, TFT_GREEN, DISPLAY_DURATION_DEFAULT);
      |     ^~~~~~~~~~~~~~~~~~
      |     DisplayMessage_t
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.cpp: In function 'void loadEmployeeLUTFromEEPROM()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.cpp:236:39: error: cannot convert 'EmployeeLUT_t*' to 'int*'
  236 |         if (loadEmployeeFromEEPROM(i, &employee_lut[loaded_count])) {
      |                                       ^~~~~~~~~~~~~~~~~~~~~~~~~~~
      |                                       |
      |                                       EmployeeLUT_t*
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.h:23:60: note:   initializing argument 2 of 'bool loadEmployeeFromEEPROM(uint32_t, int*)'
   23 | bool loadEmployeeFromEEPROM(uint32_t index, EmployeeLUT_t* employee);
      |                                             ~~~~~~~~~~~~~~~^~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.cpp: In function 'void triggerDataReExport()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.cpp:283:5: error: 'setReExportFlag' was not declared in this scope
  283 |     setReExportFlag(true);
      |     ^~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:75:46: error: macro "DEBUG_PRINTLN" passed 2 arguments, but takes just 1
   75 |     DEBUG_PRINTLN(pending_export_address, HEX);
      |                                              ^
In file included from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.h:4,
                 from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:1:
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/system_config.h:19:13: note: macro "DEBUG_PRINTLN" defined here
   19 |     #define DEBUG_PRINTLN(x) Serial.println(x)
      |             ^~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:142:44: error: macro "DEBUG_PRINTLN" passed 2 arguments, but takes just 1
  142 |             DEBUG_PRINTLN(read_address, HEX);
      |                                            ^
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/system_config.h:19:13: note: macro "DEBUG_PRINTLN" defined here
   19 |     #define DEBUG_PRINTLN(x) Serial.println(x)
      |             ^~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:149:44: error: macro "DEBUG_PRINTLN" passed 2 arguments, but takes just 1
  149 |             DEBUG_PRINTLN(read_address, HEX);
      |                                            ^
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/system_config.h:19:13: note: macro "DEBUG_PRINTLN" defined here
   19 |     #define DEBUG_PRINTLN(x) Serial.println(x)
      |             ^~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:294:44: error: macro "DEBUG_PRINT" passed 2 arguments, but takes just 1
  294 |     DEBUG_PRINT(pending_export_address, HEX);
      |                                            ^
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/system_config.h:18:13: note: macro "DEBUG_PRINT" defined here
   18 |     #define DEBUG_PRINT(x) Serial.print(x)
      |             ^~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp: In function 'void TaskExport(void*)':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:28:14: error: 'isWebServiceAvailable' was not declared in this scope; did you mean 'webservice_available'?
   28 |         if (!isWebServiceAvailable()) {
      |              ^~~~~~~~~~~~~~~~~~~~~
      |              webservice_available
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp: In function 'void loadExportState()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:65:51: error: 'EEPROM_CHIP2_ADDR_H' was not declared in this scope; did you mean 'EEPROM_CHIP1_ADDR_H'?
   65 |     pending_export_address = readUint32FromEEPROM(EEPROM_CHIP2_ADDR_H, 0x08);
      |                                                   ^~~~~~~~~~~~~~~~~~~
      |                                                   EEPROM_CHIP1_ADDR_H
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:65:30: error: 'readUint32FromEEPROM' was not declared in this scope
   65 |     pending_export_address = readUint32FromEEPROM(EEPROM_CHIP2_ADDR_H, 0x08);
      |                              ^~~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:75:5: error: 'DEBUG_PRINTLN' was not declared in this scope
   75 |     DEBUG_PRINTLN(pending_export_address, HEX);
      |     ^~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp: In function 'bool hasPendingTransactions()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:83:35: error: 'getCurrentWriteAddress' was not declared in this scope
   83 |     uint32_t current_write_addr = getCurrentWriteAddress();
      |                                   ^~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp: In function 'void exportTransactionBatch()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:114:51: error: 'TFT_GREEN' was not declared in this scope
  114 |         sendDisplayMessage("Export Success", msg, TFT_GREEN, 3000);
      |                                                   ^~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:114:9: error: 'sendDisplayMessage' was not declared in this scope; did you mean 'DisplayMessage_t'?
  114 |         sendDisplayMessage("Export Success", msg, TFT_GREEN, 3000);
      |         ^~~~~~~~~~~~~~~~~~
      |         DisplayMessage_t
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:119:60: error: 'TFT_YELLOW' was not declared in this scope
  119 |         sendDisplayMessage("Export Failed", "Retrying...", TFT_YELLOW, 3000);
      |                                                            ^~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:119:9: error: 'sendDisplayMessage' was not declared in this scope; did you mean 'DisplayMessage_t'?
  119 |         sendDisplayMessage("Export Failed", "Retrying...", TFT_YELLOW, 3000);
      |         ^~~~~~~~~~~~~~~~~~
      |         DisplayMessage_t
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp: In function 'bool readTransactionBatch(TransactionRecord_t*, uint8_t*)':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:133:35: error: 'getCurrentWriteAddress' was not declared in this scope
  133 |     uint32_t current_write_addr = getCurrentWriteAddress();
      |                                   ^~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:137:31: error: 'TRANSACTION_RECORD_SIZE' was not declared in this scope
  137 |         uint8_t record_buffer[TRANSACTION_RECORD_SIZE];
      |                               ^~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:140:30: error: 'EEPROM_CHIP2_ADDR_H' was not declared in this scope; did you mean 'EEPROM_CHIP1_ADDR_H'?
  140 |         if (!readEEPROMBlock(EEPROM_CHIP2_ADDR_H, read_address, record_buffer, TRANSACTION_RECORD_SIZE)) {
      |                              ^~~~~~~~~~~~~~~~~~~
      |                              EEPROM_CHIP1_ADDR_H
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:140:65: error: 'record_buffer' was not declared in this scope
  140 |         if (!readEEPROMBlock(EEPROM_CHIP2_ADDR_H, read_address, record_buffer, TRANSACTION_RECORD_SIZE)) {
      |                                                                 ^~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:140:14: error: 'readEEPROMBlock' was not declared in this scope
  140 |         if (!readEEPROMBlock(EEPROM_CHIP2_ADDR_H, read_address, record_buffer, TRANSACTION_RECORD_SIZE)) {
      |              ^~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:142:13: error: 'DEBUG_PRINTLN' was not declared in this scope
  142 |             DEBUG_PRINTLN(read_address, HEX);
      |             ^~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:147:37: error: 'record_buffer' was not declared in this scope
  147 |         if (!validateRecordChecksum(record_buffer)) {
      |                                     ^~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:149:13: error: 'DEBUG_PRINTLN' was not declared in this scope
  149 |             DEBUG_PRINTLN(read_address, HEX);
      |             ^~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:155:32: error: 'record_buffer' was not declared in this scope
  155 |         parseTransactionRecord(record_buffer, &records[*count]);
      |                                ^~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp: In function 'bool validateRecordChecksum(uint8_t*)':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:171:36: error: 'calculateRecordChecksum' was not declared in this scope; did you mean 'validateRecordChecksum'?
  171 |     uint32_t calculated_checksum = calculateRecordChecksum(record_buffer, 17);
      |                                    ^~~~~~~~~~~~~~~~~~~~~~~
      |                                    validateRecordChecksum
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp: In function 'bool sendRecordsToWebService(TransactionRecord_t*, uint8_t)':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:218:36: error: 'getDeviceID' was not declared in this scope
  218 |         transaction["device_id"] = getDeviceID();
      |                                    ^~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:237:13: error: 'EthernetClient' was not declared in this scope
  237 |             EthernetClient ethClient;
      |             ^~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:238:24: error: 'ethClient' was not declared in this scope; did you mean 'Client'?
  238 |             http.begin(ethClient, getExportURL());
      |                        ^~~~~~~~~
      |                        Client
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:238:35: error: 'getExportURL' was not declared in this scope
  238 |             http.begin(ethClient, getExportURL());
      |                                   ^~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:240:24: error: 'getExportURL' was not declared in this scope
  240 |             http.begin(getExportURL());
      |                        ^~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:248:41: error: 'getAuthHeader' was not declared in this scope
  248 |         http.addHeader("Authorization", getAuthHeader());
      |                                         ^~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp: In function 'void updateExportState(uint8_t)':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:285:51: error: 'TRANSACTION_RECORD_SIZE' was not declared in this scope
  285 |     pending_export_address += (exported_records * TRANSACTION_RECORD_SIZE);
      |                                                   ^~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:294:5: error: 'DEBUG_PRINT' was not declared in this scope
  294 |     DEBUG_PRINT(pending_export_address, HEX);
      |     ^~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp: In function 'void saveExportState()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:302:29: error: 'EEPROM_CHIP2_ADDR_H' was not declared in this scope; did you mean 'EEPROM_CHIP1_ADDR_H'?
  302 |         writeUint32ToEEPROM(EEPROM_CHIP2_ADDR_H, 0x08, pending_export_address);
      |                             ^~~~~~~~~~~~~~~~~~~
      |                             EEPROM_CHIP1_ADDR_H
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:302:9: error: 'writeUint32ToEEPROM' was not declared in this scope
  302 |         writeUint32ToEEPROM(EEPROM_CHIP2_ADDR_H, 0x08, pending_export_address);
      |         ^~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp: In function 'void handleReExportRequest()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:321:57: error: 'TFT_BLUE' was not declared in this scope
  321 |     sendDisplayMessage("Re-Export Started", "All Data", TFT_BLUE, 3000);
      |                                                         ^~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:321:5: error: 'sendDisplayMessage' was not declared in this scope; did you mean 'DisplayMessage_t'?
  321 |     sendDisplayMessage("Re-Export Started", "All Data", TFT_BLUE, 3000);
      |     ^~~~~~~~~~~~~~~~~~
      |     DisplayMessage_t
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp: In function 'void TaskImport(void*)':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:28:14: error: 'isWebServiceAvailable' was not declared in this scope; did you mean 'webservice_available'?
   28 |         if (!isWebServiceAvailable()) {
      |              ^~~~~~~~~~~~~~~~~~~~~
      |              webservice_available
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:42:60: error: 'TFT_GREEN' was not declared in this scope
   42 |                 sendDisplayMessage("LUT Updated", "Ready", TFT_GREEN, 3000);
      |                                                            ^~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:42:17: error: 'sendDisplayMessage' was not declared in this scope; did you mean 'DisplayMessage_t'?
   42 |                 sendDisplayMessage("LUT Updated", "Ready", TFT_GREEN, 3000);
      |                 ^~~~~~~~~~~~~~~~~~
      |                 DisplayMessage_t
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:44:72: error: 'TFT_RED' was not declared in this scope
   44 |                 sendDisplayMessage("LUT Import Failed", "Retrying...", TFT_RED, 3000);
      |                                                                        ^~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:44:17: error: 'sendDisplayMessage' was not declared in this scope; did you mean 'DisplayMessage_t'?
   44 |                 sendDisplayMessage("LUT Import Failed", "Retrying...", TFT_RED, 3000);
      |                 ^~~~~~~~~~~~~~~~~~
      |                 DisplayMessage_t
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp: In function 'void checkLUTImportRequired()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:67:24: error: 'readByteFromEEPROM' was not declared in this scope
   67 |     uint8_t lut_flag = readByteFromEEPROM(EEPROM_CHIP1_ADDR_H, 0x00);
      |                        ^~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:68:31: error: 'readUint32FromEEPROM' was not declared in this scope
   68 |     uint32_t employee_count = readUint32FromEEPROM(EEPROM_CHIP1_ADDR_H, 0x01);
      |                               ^~~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp: In function 'bool importEmployeeLUT()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:113:34: error: no matching function for call to 'min(int, uint32_t)'
  113 |         uint32_t batch_size = min(EMPLOYEE_BATCH_SIZE, total_employees - batch_start);
      |                               ~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
In file included from /home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/xtensa-esp-elf/include/c++/14.2.0/algorithm:61,
                 from /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/Arduino.h:191,
                 from /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src/NetworkInterface.h:10,
                 from /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src/Network.h:8,
                 from /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src/WiFiGeneric.h:44,
                 from /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src/WiFiSTA.h:30,
                 from /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src/WiFi.h:34,
                 from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/system_config.h:5,
                 from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.h:4,
                 from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:1:
/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/xtensa-esp-elf/include/c++/14.2.0/bits/stl_algo.h:5695:5: note: candidate: 'template<class _Tp, class _Compare> constexpr _Tp std::min(initializer_list<_Tp>, _Compare)'
 5695 |     min(initializer_list<_Tp> __l, _Compare __comp)
      |     ^~~
/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/xtensa-esp-elf/include/c++/14.2.0/bits/stl_algo.h:5695:5: note:   template argument deduction/substitution failed:
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:113:34: note:   mismatched types 'std::initializer_list<_Tp>' and 'int'
  113 |         uint32_t batch_size = min(EMPLOYEE_BATCH_SIZE, total_employees - batch_start);
      |                               ~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/xtensa-esp-elf/include/c++/14.2.0/bits/stl_algo.h:5685:5: note: candidate: 'template<class _Tp> constexpr _Tp std::min(initializer_list<_Tp>)'
 5685 |     min(initializer_list<_Tp> __l)
      |     ^~~
/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/xtensa-esp-elf/include/c++/14.2.0/bits/stl_algo.h:5685:5: note:   candidate expects 1 argument, 2 provided
In file included from /home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/xtensa-esp-elf/include/c++/14.2.0/bits/specfun.h:43,
                 from /home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/xtensa-esp-elf/include/c++/14.2.0/cmath:3898,
                 from /home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/xtensa-esp-elf/include/c++/14.2.0/math.h:36,
                 from /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal.h:30,
                 from /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src/Wire.h:33,
                 from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/system_config.h:4:
/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/xtensa-esp-elf/include/c++/14.2.0/bits/stl_algobase.h:281:5: note: candidate: 'template<class _Tp, class _Compare> constexpr const _Tp& std::min(const _Tp&, const _Tp&, _Compare)'
  281 |     min(const _Tp& __a, const _Tp& __b, _Compare __comp)
      |     ^~~
/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/xtensa-esp-elf/include/c++/14.2.0/bits/stl_algobase.h:281:5: note:   candidate expects 3 arguments, 2 provided
/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/xtensa-esp-elf/include/c++/14.2.0/bits/stl_algobase.h:233:5: note: candidate: 'template<class _Tp> constexpr const _Tp& std::min(const _Tp&, const _Tp&)'
  233 |     min(const _Tp& __a, const _Tp& __b)
      |     ^~~
/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/xtensa-esp-elf/include/c++/14.2.0/bits/stl_algobase.h:233:5: note:   template argument deduction/substitution failed:
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:113:34: note:   deduced conflicting types for parameter 'const _Tp' ('int' and 'uint32_t' {aka 'long unsigned int'})
  113 |         uint32_t batch_size = min(EMPLOYEE_BATCH_SIZE, total_employees - batch_start);
      |                               ~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:125:63: error: 'TFT_BLUE' was not declared in this scope
  125 |             sendDisplayMessage("Importing LUT", progress_msg, TFT_BLUE, 1000);
      |                                                               ^~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:125:13: error: 'sendDisplayMessage' was not declared in this scope; did you mean 'DisplayMessage_t'?
  125 |             sendDisplayMessage("Importing LUT", progress_msg, TFT_BLUE, 1000);
      |             ^~~~~~~~~~~~~~~~~~
      |             DisplayMessage_t
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp: In function 'uint32_t getEmployeeCountFromServer()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:158:13: error: 'EthernetClient' was not declared in this scope
  158 |             EthernetClient ethClient;
      |             ^~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:159:24: error: 'ethClient' was not declared in this scope; did you mean 'Client'?
  159 |             http.begin(ethClient, getEmployeeCountURL());
      |                        ^~~~~~~~~
      |                        Client
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:159:35: error: 'getEmployeeCountURL' was not declared in this scope
  159 |             http.begin(ethClient, getEmployeeCountURL());
      |                                   ^~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:161:24: error: 'getEmployeeCountURL' was not declared in this scope
  161 |             http.begin(getEmployeeCountURL());
      |                        ^~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:167:41: error: 'getAuthHeader' was not declared in this scope
  167 |         http.addHeader("Authorization", getAuthHeader());
      |                                         ^~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp: In function 'bool importEmployeeBatch(uint32_t, uint32_t, uint32_t*)':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:205:25: error: 'getEmployeeListURL' was not declared in this scope
  205 |     String url = String(getEmployeeListURL()) + "?start=" + String(start_index) +
      |                         ^~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:215:13: error: 'EthernetClient' was not declared in this scope
  215 |             EthernetClient ethClient;
      |             ^~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:216:24: error: 'ethClient' was not declared in this scope; did you mean 'Client'?
  216 |             http.begin(ethClient, url);
      |                        ^~~~~~~~~
      |                        Client
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:224:41: error: 'getAuthHeader' was not declared in this scope
  224 |         http.addHeader("Authorization", getAuthHeader());
      |                                         ^~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp: In function 'bool storeEmployeeInEEPROM(EmployeeRecord_t*, uint32_t)':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:319:12: error: 'writeEEPROMBlock' was not declared in this scope
  319 |     return writeEEPROMBlock(EEPROM_CHIP1_ADDR_H, employee_address,
      |            ^~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp: In function 'bool updateLUTMetadata(uint32_t)':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:331:10: error: 'writeByteToEEPROM' was not declared in this scope
  331 |     if (!writeByteToEEPROM(EEPROM_CHIP1_ADDR_H, 0x00, SET_FLAG)) {
      |          ^~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:336:10: error: 'writeUint32ToEEPROM' was not declared in this scope
  336 |     if (!writeUint32ToEEPROM(EEPROM_CHIP1_ADDR_H, 0x01, employee_count)) {
      |          ^~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:341:10: error: 'writeUint32ToEEPROM' was not declared in this scope
  341 |     if (!writeUint32ToEEPROM(EEPROM_CHIP1_ADDR_H, 0x05, millis())) {
      |          ^~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp: In function 'bool clearEmployeeLUTInEEPROM()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:357:5: error: 'writeByteToEEPROM' was not declared in this scope
  357 |     writeByteToEEPROM(EEPROM_CHIP1_ADDR_H, 0x00, 0x00);
      |     ^~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:360:5: error: 'writeUint32ToEEPROM' was not declared in this scope
  360 |     writeUint32ToEEPROM(EEPROM_CHIP1_ADDR_H, 0x01, 0);
      |     ^~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:367:9: error: 'writeEEPROMBlock' was not declared in this scope
  367 |         writeEEPROMBlock(EEPROM_CHIP1_ADDR_H, addr, zero_buffer, 64);
      |         ^~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp: In function 'void importDeviceConfiguration()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:397:9: error: 'EthernetClient' was not declared in this scope
  397 |         EthernetClient ethClient;
      |         ^~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:398:20: error: 'ethClient' was not declared in this scope; did you mean 'Client'?
  398 |         http.begin(ethClient, getDeviceConfigURL());
      |                    ^~~~~~~~~
      |                    Client
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:398:31: error: 'getDeviceConfigURL' was not declared in this scope
  398 |         http.begin(ethClient, getDeviceConfigURL());
      |                               ^~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:400:20: error: 'getDeviceConfigURL' was not declared in this scope
  400 |         http.begin(getDeviceConfigURL());
      |                    ^~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:406:37: error: 'getAuthHeader' was not declared in this scope
  406 |     http.addHeader("Authorization", getAuthHeader());
      |                                     ^~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp: In function 'bool parseAndApplyDeviceConfig(const String&)':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:469:57: error: 'TFT_GREEN' was not declared in this scope
  469 |         sendDisplayMessage("Config Updated", "Applied", TFT_GREEN, 2000);
      |                                                         ^~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:469:9: error: 'sendDisplayMessage' was not declared in this scope; did you mean 'DisplayMessage_t'?
  469 |         sendDisplayMessage("Config Updated", "Applied", TFT_GREEN, 2000);
      |         ^~~~~~~~~~~~~~~~~~
      |         DisplayMessage_t
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp: In function 'void syncTimeWithServer()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:491:9: error: 'EthernetClient' was not declared in this scope
  491 |         EthernetClient ethClient;
      |         ^~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:492:20: error: 'ethClient' was not declared in this scope; did you mean 'Client'?
  492 |         http.begin(ethClient, getTimeServerURL());
      |                    ^~~~~~~~~
      |                    Client
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:492:31: error: 'getTimeServerURL' was not declared in this scope
  492 |         http.begin(ethClient, getTimeServerURL());
      |                               ^~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:494:20: error: 'getTimeServerURL' was not declared in this scope
  494 |         http.begin(getTimeServerURL());
      |                    ^~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:500:37: error: 'getAuthHeader' was not declared in this scope
  500 |     http.addHeader("Authorization", getAuthHeader());
      |                                     ^~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:517:17: error: 'updateSystemTime' was not declared in this scope
  517 |                 updateSystemTime(server_time);
      |                 ^~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:519:61: error: 'TFT_GREEN' was not declared in this scope
  519 |                 sendDisplayMessage("Time Synced", "Server", TFT_GREEN, 2000);
      |                                                             ^~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:519:17: error: 'sendDisplayMessage' was not declared in this scope; did you mean 'DisplayMessage_t'?
  519 |                 sendDisplayMessage("Time Synced", "Server", TFT_GREEN, 2000);
      |                 ^~~~~~~~~~~~~~~~~~
      |                 DisplayMessage_t
In file included from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_tft.h:5,
                 from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_tft.cpp:1:
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/libraries/TFT_eSPI-master/TFT_eSPI.h:306:21: error: 'VSPI' was not declared in this scope; did you mean 'SPI'?
  306 |     uint8_t  port = VSPI;
      |                     ^~~~
      |                     SPI
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_tft.h:24:6: error: variable or field 'processDisplayMessage' declared void
   24 | void processDisplayMessage(DisplayMessage_t* msg);
      |      ^~~~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_tft.h:24:28: error: 'DisplayMessage_t' was not declared in this scope
   24 | void processDisplayMessage(DisplayMessage_t* msg);
      |                            ^~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_tft.h:24:46: error: 'msg' was not declared in this scope
   24 | void processDisplayMessage(DisplayMessage_t* msg);
      |                                              ^~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_tft.cpp: In function 'void TaskTFT(void*)':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_tft.cpp:47:13: error: 'processDisplayMessage' was not declared in this scope; did you mean 'sendDisplayMessage'?
   47 |             processDisplayMessage(&display_msg);
      |             ^~~~~~~~~~~~~~~~~~~~~
      |             sendDisplayMessage
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_tft.cpp: In function 'void processDisplayMessage(DisplayMessage_t*)':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_tft.cpp:146:9: error: 'sendGPIOCommand' was not declared in this scope
  146 |         sendGPIOCommand(true, false, true, false, 1000); // OK LED + Beep
      |         ^~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_tft.cpp:148:9: error: 'sendGPIOCommand' was not declared in this scope
  148 |         sendGPIOCommand(false, true, true, false, 2000); // Error LED + Beep
      |         ^~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_tft.cpp: In function 'void updateNetworkStatus()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_tft.cpp:228:9: error: 'isWebServiceAvailable' was not declared in this scope; did you mean 'webservice_available'?
  228 |     if (isWebServiceAvailable()) {
      |         ^~~~~~~~~~~~~~~~~~~~~
      |         webservice_available
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:13:13: error: 'wifi_connected' was declared 'extern' and later 'static' [-fpermissive]
   13 | static bool wifi_connected = false;
      |             ^~~~~~~~~~~~~~
In file included from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:1:
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.h:41:13: note: previous declaration of 'wifi_connected'
   41 | extern bool wifi_connected;
      |             ^~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:14:13: error: 'ethernet_connected' was declared 'extern' and later 'static' [-fpermissive]
   14 | static bool ethernet_connected = false;
      |             ^~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.h:42:13: note: previous declaration of 'ethernet_connected'
   42 | extern bool ethernet_connected;
      |             ^~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp: In function 'void TaskWeb(void*)':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:36:9: error: 'checkWebServiceAvailability' was not declared in this scope; did you mean 'isWebServiceAvailable'?
   36 |         checkWebServiceAvailability();
      |         ^~~~~~~~~~~~~~~~~~~~~~~~~~~
      |         isWebServiceAvailable
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:39:9: error: 'handleConfigurationMode' was not declared in this scope
   39 |         handleConfigurationMode();
      |         ^~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:45:9: error: 'updateNetworkStatusDisplay' was not declared in this scope; did you mean 'updateNetworkStatus'?
   45 |         updateNetworkStatusDisplay();
      |         ^~~~~~~~~~~~~~~~~~~~~~~~~~
      |         updateNetworkStatus
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp: In function 'bool initializeNetworking()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:65:5: error: 'loadWebServiceConfig' was not declared in this scope
   65 |     loadWebServiceConfig();
      |     ^~~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp: In function 'void checkWebServiceAvailability()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:200:60: error: 'TFT_GREEN' was not declared in this scope
  200 |             sendDisplayMessage("Web Service", "Connected", TFT_GREEN, 3000);
      |                                                            ^~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:200:13: error: 'sendDisplayMessage' was not declared in this scope; did you mean 'DisplayMessage_t'?
  200 |             sendDisplayMessage("Web Service", "Connected", TFT_GREEN, 3000);
      |             ^~~~~~~~~~~~~~~~~~
      |             DisplayMessage_t
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:202:63: error: 'TFT_YELLOW' was not declared in this scope
  202 |             sendDisplayMessage("Web Service", "Disconnected", TFT_YELLOW, 3000);
      |                                                               ^~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:202:13: error: 'sendDisplayMessage' was not declared in this scope; did you mean 'DisplayMessage_t'?
  202 |             sendDisplayMessage("Web Service", "Disconnected", TFT_YELLOW, 3000);
      |             ^~~~~~~~~~~~~~~~~~
      |             DisplayMessage_t
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp: In function 'bool testWebServiceConnection()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:219:19: error: no matching function for call to 'HTTPClient::begin(EthernetClient&, char [100])'
  219 |         http.begin(ethClient, web_service_url);
      |         ~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~
In file included from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.h:6:
/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src/HTTPClient.h:182:8: note: candidate: 'bool HTTPClient::begin(NetworkClient&, String)'
  182 |   bool begin(NetworkClient &client, String url);
      |        ^~~~~
/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src/HTTPClient.h:182:29: note:   no known conversion for argument 1 from 'EthernetClient' to 'NetworkClient&'
  182 |   bool begin(NetworkClient &client, String url);
      |              ~~~~~~~~~~~~~~~^~~~~~
/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src/HTTPClient.h:183:8: note: candidate: 'bool HTTPClient::begin(NetworkClient&, String, uint16_t, String, bool)'
  183 |   bool begin(NetworkClient &client, String host, uint16_t port, String uri = "/", bool https = false);
      |        ^~~~~
/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src/HTTPClient.h:183:8: note:   candidate expects 5 arguments, 2 provided
/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src/HTTPClient.h:186:8: note: candidate: 'bool HTTPClient::begin(String)'
  186 |   bool begin(String url);
      |        ^~~~~
/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src/HTTPClient.h:186:8: note:   candidate expects 1 argument, 2 provided
/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src/HTTPClient.h:187:8: note: candidate: 'bool HTTPClient::begin(String, uint16_t, String)'
  187 |   bool begin(String host, uint16_t port, String uri = "/");
      |        ^~~~~
/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src/HTTPClient.h:187:21: note:   no known conversion for argument 1 from 'EthernetClient' to 'String'
  187 |   bool begin(String host, uint16_t port, String uri = "/");
      |              ~~~~~~~^~~~
/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src/HTTPClient.h:189:8: note: candidate: 'bool HTTPClient::begin(String, const char*)'
  189 |   bool begin(String url, const char *CAcert);
      |        ^~~~~
/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src/HTTPClient.h:189:21: note:   no known conversion for argument 1 from 'EthernetClient' to 'String'
  189 |   bool begin(String url, const char *CAcert);
      |              ~~~~~~~^~~
/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src/HTTPClient.h:190:8: note: candidate: 'bool HTTPClient::begin(String, uint16_t, String, const char*)'
  190 |   bool begin(String host, uint16_t port, String uri, const char *CAcert);
      |        ^~~~~
/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src/HTTPClient.h:190:8: note:   candidate expects 4 arguments, 2 provided
/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src/HTTPClient.h:191:8: note: candidate: 'bool HTTPClient::begin(String, uint16_t, String, const char*, const char*, const char*)'
  191 |   bool begin(String host, uint16_t port, String uri, const char *CAcert, const char *cli_cert, const char *cli_key);
      |        ^~~~~
/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src/HTTPClient.h:191:8: note:   candidate expects 6 arguments, 2 provided
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp: In function 'void handleConfigurationMode()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:259:28: error: 'checkConfigModeFlag' was not declared in this scope
  259 |     bool should_activate = checkConfigModeFlag();
      |                            ^~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:266:56: error: 'TFT_BLUE' was not declared in this scope
  266 |         sendDisplayMessage("Config Mode", "2 minutes", TFT_BLUE, 0);
      |                                                        ^~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:266:9: error: 'sendDisplayMessage' was not declared in this scope; did you mean 'DisplayMessage_t'?
  266 |         sendDisplayMessage("Config Mode", "2 minutes", TFT_BLUE, 0);
      |         ^~~~~~~~~~~~~~~~~~
      |         DisplayMessage_t
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:269:9: error: 'startConfigurationWebServer' was not declared in this scope
  269 |         startConfigurationWebServer();
      |         ^~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:278:68: error: 'TFT_GREEN' was not declared in this scope
  278 |             sendDisplayMessage("Config Complete", "Restarting...", TFT_GREEN, 3000);
      |                                                                    ^~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:278:13: error: 'sendDisplayMessage' was not declared in this scope; did you mean 'DisplayMessage_t'?
  278 |             sendDisplayMessage("Config Complete", "Restarting...", TFT_GREEN, 3000);
      |             ^~~~~~~~~~~~~~~~~~
      |             DisplayMessage_t
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:281:13: error: 'stopConfigurationWebServer' was not declared in this scope
  281 |             stopConfigurationWebServer();
      |             ^~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:284:13: error: 'clearConfigModeFlag' was not declared in this scope
  284 |             clearConfigModeFlag();
      |             ^~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:291:13: error: 'processConfigurationRequests' was not declared in this scope
  291 |             processConfigurationRequests();
      |             ^~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp: In function 'void handleUDPDiscovery()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:327:9: error: 'sendUDPDiscoveryResponse' was not declared in this scope
  327 |         sendUDPDiscoveryResponse(&udp);
      |         ^~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp: In function 'void sendUDPDiscoveryResponse(WiFiUDP*)':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:335:31: error: 'getDeviceName' was not declared in this scope; did you mean 'getDeviceID'?
  335 |     response["device_name"] = getDeviceName();
      |                               ^~~~~~~~~~~~~
      |                               getDeviceID
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:338:36: error: 'getFirmwareVersion' was not declared in this scope
  338 |     response["firmware_version"] = getFirmwareVersion();
      |                                    ^~~~~~~~~~~~~~~~~~
exit status 1

Compilation error: expected unqualified-id before numeric constant
