/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/hardware_init.cpp: In function 'bool initializeSPI()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/hardware_init.cpp:79:10: error: request for member 'begin' in '0', which is of non-class type 'int'
   79 |     FSPI.begin();
      |          ^~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/hardware_init.cpp: In function 'bool testSPIDevices()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/hardware_init.cpp:202:10: error: request for member 'transfer' in '0', which is of non-class type 'int'
  202 |     FSPI.transfer(0x00); // Dummy transfer
      |          ^~~~~~~~
In file included from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_display.h:5,
                 from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_smartcard.cpp:3:
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/libraries/TFT_eSPI-master/TFT_eSPI.h:306:21: error: 'VSPI' was not declared in this scope; did you mean 'SPI'?
  306 |     uint8_t  port = VSPI;
      |                     ^~~~
      |                     SPI
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_smartcard.cpp: In function 'bool validateAndParseCard(uint8_t*, CardData_t*)':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_smartcard.cpp:174:28: error: 'EPOCH_TIME' was not declared in this scope; did you mean 'LC_TIME'?
  174 |     card_data->timestamp = EPOCH_TIME;
      |                            ^~~~~~~~~~
      |                            LC_TIME
In file included from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_display.h:5,
                 from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.cpp:3:
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/libraries/TFT_eSPI-master/TFT_eSPI.h:306:21: error: 'VSPI' was not declared in this scope; did you mean 'SPI'?
  306 |     uint8_t  port = VSPI;
      |                     ^~~~
      |                     SPI
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.cpp:11:3: error: conflicting declaration 'typedef struct EmployeeLUT_t EmployeeLUT_t'
   11 | } EmployeeLUT_t;
      |   ^~~~~~~~~~~~~
In file included from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.cpp:1:
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.h:14:3: note: previous declaration as 'typedef struct EmployeeLUT_t EmployeeLUT_t'
   14 | } EmployeeLUT_t;
      |   ^~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.cpp: In function 'void triggerDataReExport()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_carddata.cpp:284:5: error: 'setReExportFlag' was not declared in this scope
  284 |     setReExportFlag(true);
      |     ^~~~~~~~~~~~~~~
In file included from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_display.h:5,
                 from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_eeprom.cpp:3:
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/libraries/TFT_eSPI-master/TFT_eSPI.h:306:21: error: 'VSPI' was not declared in this scope; did you mean 'SPI'?
  306 |     uint8_t  port = VSPI;
      |                     ^~~~
      |                     SPI
In file included from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_display.h:5,
                 from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/main_application.cpp:6:
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/libraries/TFT_eSPI-master/TFT_eSPI.h:306:21: error: 'VSPI' was not declared in this scope; did you mean 'SPI'?
  306 |     uint8_t  port = VSPI;
      |                     ^~~~
      |                     SPI
In file included from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/main_application.cpp:7:
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_tft.h:36:6: error: ambiguating new declaration of 'bool sendDisplayMessage(const char*, const char*, uint16_t, uint32_t)'
   36 | bool sendDisplayMessage(const char* line1, const char* line2, uint16_t color, uint32_t duration_ms);
      |      ^~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_display.h:8:6: note: old declaration 'void sendDisplayMessage(const char*, const char*, uint16_t, uint32_t)'
    8 | void sendDisplayMessage(const char* title, const char* message, uint16_t color, uint32_t duration);
      |      ^~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp: In function 'void TaskExport(void*)':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:28:14: error: 'isWebServiceAvailable' was not declared in this scope; did you mean 'webservice_available'?
   28 |         if (!isWebServiceAvailable()) {
      |              ^~~~~~~~~~~~~~~~~~~~~
      |              webservice_available
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp: In function 'void loadExportState()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:65:51: error: 'EEPROM_CHIP2_ADDR_H' was not declared in this scope; did you mean 'EEPROM_CHIP1_ADDR_H'?
   65 |     pending_export_address = readUint32FromEEPROM(EEPROM_CHIP2_ADDR_H, 0x08);
      |                                                   ^~~~~~~~~~~~~~~~~~~
      |                                                   EEPROM_CHIP1_ADDR_H
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:65:30: error: 'readUint32FromEEPROM' was not declared in this scope
   65 |     pending_export_address = readUint32FromEEPROM(EEPROM_CHIP2_ADDR_H, 0x08);
      |                              ^~~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp: In function 'bool hasPendingTransactions()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:83:35: error: 'getCurrentWriteAddress' was not declared in this scope
   83 |     uint32_t current_write_addr = getCurrentWriteAddress();
      |                                   ^~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp: In function 'void exportTransactionBatch()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:114:51: error: 'TFT_GREEN' was not declared in this scope
  114 |         sendDisplayMessage("Export Success", msg, TFT_GREEN, 3000);
      |                                                   ^~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:114:9: error: 'sendDisplayMessage' was not declared in this scope; did you mean 'DisplayMessage_t'?
  114 |         sendDisplayMessage("Export Success", msg, TFT_GREEN, 3000);
      |         ^~~~~~~~~~~~~~~~~~
      |         DisplayMessage_t
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:119:60: error: 'TFT_YELLOW' was not declared in this scope
  119 |         sendDisplayMessage("Export Failed", "Retrying...", TFT_YELLOW, 3000);
      |                                                            ^~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:119:9: error: 'sendDisplayMessage' was not declared in this scope; did you mean 'DisplayMessage_t'?
  119 |         sendDisplayMessage("Export Failed", "Retrying...", TFT_YELLOW, 3000);
      |         ^~~~~~~~~~~~~~~~~~
      |         DisplayMessage_t
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp: In function 'bool readTransactionBatch(TransactionRecord_t*, uint8_t*)':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:133:35: error: 'getCurrentWriteAddress' was not declared in this scope
  133 |     uint32_t current_write_addr = getCurrentWriteAddress();
      |                                   ^~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:137:31: error: 'TRANSACTION_RECORD_SIZE' was not declared in this scope
  137 |         uint8_t record_buffer[TRANSACTION_RECORD_SIZE];
      |                               ^~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:140:30: error: 'EEPROM_CHIP2_ADDR_H' was not declared in this scope; did you mean 'EEPROM_CHIP1_ADDR_H'?
  140 |         if (!readEEPROMBlock(EEPROM_CHIP2_ADDR_H, read_address, record_buffer, TRANSACTION_RECORD_SIZE)) {
      |                              ^~~~~~~~~~~~~~~~~~~
      |                              EEPROM_CHIP1_ADDR_H
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:140:65: error: 'record_buffer' was not declared in this scope
  140 |         if (!readEEPROMBlock(EEPROM_CHIP2_ADDR_H, read_address, record_buffer, TRANSACTION_RECORD_SIZE)) {
      |                                                                 ^~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:140:14: error: 'readEEPROMBlock' was not declared in this scope
  140 |         if (!readEEPROMBlock(EEPROM_CHIP2_ADDR_H, read_address, record_buffer, TRANSACTION_RECORD_SIZE)) {
      |              ^~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:147:37: error: 'record_buffer' was not declared in this scope
  147 |         if (!validateRecordChecksum(record_buffer)) {
      |                                     ^~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:155:32: error: 'record_buffer' was not declared in this scope
  155 |         parseTransactionRecord(record_buffer, &records[*count]);
      |                                ^~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp: In function 'bool validateRecordChecksum(uint8_t*)':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:171:36: error: 'calculateRecordChecksum' was not declared in this scope; did you mean 'validateRecordChecksum'?
  171 |     uint32_t calculated_checksum = calculateRecordChecksum(record_buffer, 17);
      |                                    ^~~~~~~~~~~~~~~~~~~~~~~
      |                                    validateRecordChecksum
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp: In function 'bool sendRecordsToWebService(TransactionRecord_t*, uint8_t)':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:218:36: error: 'getDeviceID' was not declared in this scope
  218 |         transaction["device_id"] = getDeviceID();
      |                                    ^~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:237:13: error: 'EthernetClient' was not declared in this scope
  237 |             EthernetClient ethClient;
      |             ^~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:238:24: error: 'ethClient' was not declared in this scope; did you mean 'Client'?
  238 |             http.begin(ethClient, getExportURL());
      |                        ^~~~~~~~~
      |                        Client
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:238:35: error: 'getExportURL' was not declared in this scope
  238 |             http.begin(ethClient, getExportURL());
      |                                   ^~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:240:24: error: 'getExportURL' was not declared in this scope
  240 |             http.begin(getExportURL());
      |                        ^~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:248:41: error: 'getAuthHeader' was not declared in this scope
  248 |         http.addHeader("Authorization", getAuthHeader());
      |                                         ^~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp: In function 'void updateExportState(uint8_t)':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:285:51: error: 'TRANSACTION_RECORD_SIZE' was not declared in this scope
  285 |     pending_export_address += (exported_records * TRANSACTION_RECORD_SIZE);
      |                                                   ^~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp: In function 'void saveExportState()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:302:29: error: 'EEPROM_CHIP2_ADDR_H' was not declared in this scope; did you mean 'EEPROM_CHIP1_ADDR_H'?
  302 |         writeUint32ToEEPROM(EEPROM_CHIP2_ADDR_H, 0x08, pending_export_address);
      |                             ^~~~~~~~~~~~~~~~~~~
      |                             EEPROM_CHIP1_ADDR_H
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:302:9: error: 'writeUint32ToEEPROM' was not declared in this scope
  302 |         writeUint32ToEEPROM(EEPROM_CHIP2_ADDR_H, 0x08, pending_export_address);
      |         ^~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp: In function 'void handleReExportRequest()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:321:57: error: 'TFT_BLUE' was not declared in this scope
  321 |     sendDisplayMessage("Re-Export Started", "All Data", TFT_BLUE, 3000);
      |                                                         ^~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_export.cpp:321:5: error: 'sendDisplayMessage' was not declared in this scope; did you mean 'DisplayMessage_t'?
  321 |     sendDisplayMessage("Re-Export Started", "All Data", TFT_BLUE, 3000);
      |     ^~~~~~~~~~~~~~~~~~
      |     DisplayMessage_t
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp: In function 'void TaskImport(void*)':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:28:14: error: 'isWebServiceAvailable' was not declared in this scope; did you mean 'webservice_available'?
   28 |         if (!isWebServiceAvailable()) {
      |              ^~~~~~~~~~~~~~~~~~~~~
      |              webservice_available
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:42:60: error: 'TFT_GREEN' was not declared in this scope
   42 |                 sendDisplayMessage("LUT Updated", "Ready", TFT_GREEN, 3000);
      |                                                            ^~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:42:17: error: 'sendDisplayMessage' was not declared in this scope; did you mean 'DisplayMessage_t'?
   42 |                 sendDisplayMessage("LUT Updated", "Ready", TFT_GREEN, 3000);
      |                 ^~~~~~~~~~~~~~~~~~
      |                 DisplayMessage_t
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:44:72: error: 'TFT_RED' was not declared in this scope
   44 |                 sendDisplayMessage("LUT Import Failed", "Retrying...", TFT_RED, 3000);
      |                                                                        ^~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:44:17: error: 'sendDisplayMessage' was not declared in this scope; did you mean 'DisplayMessage_t'?
   44 |                 sendDisplayMessage("LUT Import Failed", "Retrying...", TFT_RED, 3000);
      |                 ^~~~~~~~~~~~~~~~~~
      |                 DisplayMessage_t
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp: In function 'void checkLUTImportRequired()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:67:24: error: 'readByteFromEEPROM' was not declared in this scope
   67 |     uint8_t lut_flag = readByteFromEEPROM(EEPROM_CHIP1_ADDR_H, 0x00);
      |                        ^~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:68:31: error: 'readUint32FromEEPROM' was not declared in this scope
   68 |     uint32_t employee_count = readUint32FromEEPROM(EEPROM_CHIP1_ADDR_H, 0x01);
      |                               ^~~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp: In function 'bool importEmployeeLUT()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:113:34: error: no matching function for call to 'min(int, uint32_t)'
  113 |         uint32_t batch_size = min(EMPLOYEE_BATCH_SIZE, total_employees - batch_start);
      |                               ~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
In file included from /home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/xtensa-esp-elf/include/c++/14.2.0/algorithm:61,
                 from /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/Arduino.h:191,
                 from /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src/NetworkInterface.h:10,
                 from /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src/Network.h:8,
                 from /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src/WiFiGeneric.h:44,
                 from /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src/WiFiSTA.h:30,
                 from /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src/WiFi.h:34,
                 from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/system_config.h:5,
                 from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.h:4,
                 from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:1:
/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/xtensa-esp-elf/include/c++/14.2.0/bits/stl_algo.h:5695:5: note: candidate: 'template<class _Tp, class _Compare> constexpr _Tp std::min(initializer_list<_Tp>, _Compare)'
 5695 |     min(initializer_list<_Tp> __l, _Compare __comp)
      |     ^~~
/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/xtensa-esp-elf/include/c++/14.2.0/bits/stl_algo.h:5695:5: note:   template argument deduction/substitution failed:
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:113:34: note:   mismatched types 'std::initializer_list<_Tp>' and 'int'
  113 |         uint32_t batch_size = min(EMPLOYEE_BATCH_SIZE, total_employees - batch_start);
      |                               ~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/xtensa-esp-elf/include/c++/14.2.0/bits/stl_algo.h:5685:5: note: candidate: 'template<class _Tp> constexpr _Tp std::min(initializer_list<_Tp>)'
 5685 |     min(initializer_list<_Tp> __l)
      |     ^~~
/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/xtensa-esp-elf/include/c++/14.2.0/bits/stl_algo.h:5685:5: note:   candidate expects 1 argument, 2 provided
In file included from /home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/xtensa-esp-elf/include/c++/14.2.0/bits/specfun.h:43,
                 from /home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/xtensa-esp-elf/include/c++/14.2.0/cmath:3898,
                 from /home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/xtensa-esp-elf/include/c++/14.2.0/math.h:36,
                 from /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal.h:30,
                 from /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src/Wire.h:33,
                 from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/system_config.h:4:
/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/xtensa-esp-elf/include/c++/14.2.0/bits/stl_algobase.h:281:5: note: candidate: 'template<class _Tp, class _Compare> constexpr const _Tp& std::min(const _Tp&, const _Tp&, _Compare)'
  281 |     min(const _Tp& __a, const _Tp& __b, _Compare __comp)
      |     ^~~
/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/xtensa-esp-elf/include/c++/14.2.0/bits/stl_algobase.h:281:5: note:   candidate expects 3 arguments, 2 provided
/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/xtensa-esp-elf/include/c++/14.2.0/bits/stl_algobase.h:233:5: note: candidate: 'template<class _Tp> constexpr const _Tp& std::min(const _Tp&, const _Tp&)'
  233 |     min(const _Tp& __a, const _Tp& __b)
      |     ^~~
/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/xtensa-esp-elf/include/c++/14.2.0/bits/stl_algobase.h:233:5: note:   template argument deduction/substitution failed:
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:113:34: note:   deduced conflicting types for parameter 'const _Tp' ('int' and 'uint32_t' {aka 'long unsigned int'})
  113 |         uint32_t batch_size = min(EMPLOYEE_BATCH_SIZE, total_employees - batch_start);
      |                               ~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:125:63: error: 'TFT_BLUE' was not declared in this scope
  125 |             sendDisplayMessage("Importing LUT", progress_msg, TFT_BLUE, 1000);
      |                                                               ^~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:125:13: error: 'sendDisplayMessage' was not declared in this scope; did you mean 'DisplayMessage_t'?
  125 |             sendDisplayMessage("Importing LUT", progress_msg, TFT_BLUE, 1000);
      |             ^~~~~~~~~~~~~~~~~~
      |             DisplayMessage_t
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp: In function 'uint32_t getEmployeeCountFromServer()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:158:13: error: 'EthernetClient' was not declared in this scope
  158 |             EthernetClient ethClient;
      |             ^~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:159:24: error: 'ethClient' was not declared in this scope; did you mean 'Client'?
  159 |             http.begin(ethClient, getEmployeeCountURL());
      |                        ^~~~~~~~~
      |                        Client
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:159:35: error: 'getEmployeeCountURL' was not declared in this scope
  159 |             http.begin(ethClient, getEmployeeCountURL());
      |                                   ^~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:161:24: error: 'getEmployeeCountURL' was not declared in this scope
  161 |             http.begin(getEmployeeCountURL());
      |                        ^~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:167:41: error: 'getAuthHeader' was not declared in this scope
  167 |         http.addHeader("Authorization", getAuthHeader());
      |                                         ^~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp: In function 'bool importEmployeeBatch(uint32_t, uint32_t, uint32_t*)':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:205:25: error: 'getEmployeeListURL' was not declared in this scope
  205 |     String url = String(getEmployeeListURL()) + "?start=" + String(start_index) +
      |                         ^~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:215:13: error: 'EthernetClient' was not declared in this scope
  215 |             EthernetClient ethClient;
      |             ^~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:216:24: error: 'ethClient' was not declared in this scope; did you mean 'Client'?
  216 |             http.begin(ethClient, url);
      |                        ^~~~~~~~~
      |                        Client
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:224:41: error: 'getAuthHeader' was not declared in this scope
  224 |         http.addHeader("Authorization", getAuthHeader());
      |                                         ^~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp: In function 'bool storeEmployeeInEEPROM(EmployeeRecord_t*, uint32_t)':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:319:12: error: 'writeEEPROMBlock' was not declared in this scope
  319 |     return writeEEPROMBlock(EEPROM_CHIP1_ADDR_H, employee_address,
      |            ^~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp: In function 'bool updateLUTMetadata(uint32_t)':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:331:10: error: 'writeByteToEEPROM' was not declared in this scope
  331 |     if (!writeByteToEEPROM(EEPROM_CHIP1_ADDR_H, 0x00, SET_FLAG)) {
      |          ^~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:336:10: error: 'writeUint32ToEEPROM' was not declared in this scope
  336 |     if (!writeUint32ToEEPROM(EEPROM_CHIP1_ADDR_H, 0x01, employee_count)) {
      |          ^~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:341:10: error: 'writeUint32ToEEPROM' was not declared in this scope
  341 |     if (!writeUint32ToEEPROM(EEPROM_CHIP1_ADDR_H, 0x05, millis())) {
      |          ^~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp: In function 'bool clearEmployeeLUTInEEPROM()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:357:5: error: 'writeByteToEEPROM' was not declared in this scope
  357 |     writeByteToEEPROM(EEPROM_CHIP1_ADDR_H, 0x00, 0x00);
      |     ^~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:360:5: error: 'writeUint32ToEEPROM' was not declared in this scope
  360 |     writeUint32ToEEPROM(EEPROM_CHIP1_ADDR_H, 0x01, 0);
      |     ^~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:367:9: error: 'writeEEPROMBlock' was not declared in this scope
  367 |         writeEEPROMBlock(EEPROM_CHIP1_ADDR_H, addr, zero_buffer, 64);
      |         ^~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp: In function 'void importDeviceConfiguration()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:397:9: error: 'EthernetClient' was not declared in this scope
  397 |         EthernetClient ethClient;
      |         ^~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:398:20: error: 'ethClient' was not declared in this scope; did you mean 'Client'?
  398 |         http.begin(ethClient, getDeviceConfigURL());
      |                    ^~~~~~~~~
      |                    Client
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:398:31: error: 'getDeviceConfigURL' was not declared in this scope
  398 |         http.begin(ethClient, getDeviceConfigURL());
      |                               ^~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:400:20: error: 'getDeviceConfigURL' was not declared in this scope
  400 |         http.begin(getDeviceConfigURL());
      |                    ^~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:406:37: error: 'getAuthHeader' was not declared in this scope
  406 |     http.addHeader("Authorization", getAuthHeader());
      |                                     ^~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp: In function 'bool parseAndApplyDeviceConfig(const String&)':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:469:57: error: 'TFT_GREEN' was not declared in this scope
  469 |         sendDisplayMessage("Config Updated", "Applied", TFT_GREEN, 2000);
      |                                                         ^~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:469:9: error: 'sendDisplayMessage' was not declared in this scope; did you mean 'DisplayMessage_t'?
  469 |         sendDisplayMessage("Config Updated", "Applied", TFT_GREEN, 2000);
      |         ^~~~~~~~~~~~~~~~~~
      |         DisplayMessage_t
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp: In function 'void syncTimeWithServer()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:491:9: error: 'EthernetClient' was not declared in this scope
  491 |         EthernetClient ethClient;
      |         ^~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:492:20: error: 'ethClient' was not declared in this scope; did you mean 'Client'?
  492 |         http.begin(ethClient, getTimeServerURL());
      |                    ^~~~~~~~~
      |                    Client
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:492:31: error: 'getTimeServerURL' was not declared in this scope
  492 |         http.begin(ethClient, getTimeServerURL());
      |                               ^~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:494:20: error: 'getTimeServerURL' was not declared in this scope
  494 |         http.begin(getTimeServerURL());
      |                    ^~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:500:37: error: 'getAuthHeader' was not declared in this scope
  500 |     http.addHeader("Authorization", getAuthHeader());
      |                                     ^~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:517:17: error: 'updateSystemTime' was not declared in this scope
  517 |                 updateSystemTime(server_time);
      |                 ^~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:519:61: error: 'TFT_GREEN' was not declared in this scope
  519 |                 sendDisplayMessage("Time Synced", "Server", TFT_GREEN, 2000);
      |                                                             ^~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_import.cpp:519:17: error: 'sendDisplayMessage' was not declared in this scope; did you mean 'DisplayMessage_t'?
  519 |                 sendDisplayMessage("Time Synced", "Server", TFT_GREEN, 2000);
      |                 ^~~~~~~~~~~~~~~~~~
      |                 DisplayMessage_t
In file included from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_tft.h:5,
                 from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_tft.cpp:1:
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/libraries/TFT_eSPI-master/TFT_eSPI.h:306:21: error: 'VSPI' was not declared in this scope; did you mean 'SPI'?
  306 |     uint8_t  port = VSPI;
      |                     ^~~~
      |                     SPI
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_tft.h:29:6: error: variable or field 'processDisplayMessage' declared void
   29 | void processDisplayMessage(DisplayMessage_t* msg);
      |      ^~~~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_tft.h:29:28: error: 'DisplayMessage_t' was not declared in this scope
   29 | void processDisplayMessage(DisplayMessage_t* msg);
      |                            ^~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_tft.h:29:46: error: 'msg' was not declared in this scope
   29 | void processDisplayMessage(DisplayMessage_t* msg);
      |                                              ^~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_tft.cpp: In function 'void TaskTFT(void*)':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_tft.cpp:48:13: error: 'processDisplayMessage' was not declared in this scope; did you mean 'sendDisplayMessage'?
   48 |             processDisplayMessage(&display_msg);
      |             ^~~~~~~~~~~~~~~~~~~~~
      |             sendDisplayMessage
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_tft.cpp: In function 'void processDisplayMessage(DisplayMessage_t*)':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_tft.cpp:147:9: error: 'sendGPIOCommand' was not declared in this scope
  147 |         sendGPIOCommand(true, false, true, false, 1000); // OK LED + Beep
      |         ^~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_tft.cpp:149:9: error: 'sendGPIOCommand' was not declared in this scope
  149 |         sendGPIOCommand(false, true, true, false, 2000); // Error LED + Beep
      |         ^~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:13:13: error: 'wifi_connected' was declared 'extern' and later 'static' [-fpermissive]
   13 | static bool wifi_connected = false;
      |             ^~~~~~~~~~~~~~
In file included from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:1:
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.h:41:13: note: previous declaration of 'wifi_connected'
   41 | extern bool wifi_connected;
      |             ^~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:14:13: error: 'ethernet_connected' was declared 'extern' and later 'static' [-fpermissive]
   14 | static bool ethernet_connected = false;
      |             ^~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.h:42:13: note: previous declaration of 'ethernet_connected'
   42 | extern bool ethernet_connected;
      |             ^~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp: In function 'void TaskWeb(void*)':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:36:9: error: 'checkWebServiceAvailability' was not declared in this scope; did you mean 'isWebServiceAvailable'?
   36 |         checkWebServiceAvailability();
      |         ^~~~~~~~~~~~~~~~~~~~~~~~~~~
      |         isWebServiceAvailable
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:39:9: error: 'handleConfigurationMode' was not declared in this scope
   39 |         handleConfigurationMode();
      |         ^~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:45:9: error: 'updateNetworkStatusDisplay' was not declared in this scope; did you mean 'updateNetworkStatus'?
   45 |         updateNetworkStatusDisplay();
      |         ^~~~~~~~~~~~~~~~~~~~~~~~~~
      |         updateNetworkStatus
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp: In function 'bool initializeNetworking()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:65:5: error: 'loadWebServiceConfig' was not declared in this scope
   65 |     loadWebServiceConfig();
      |     ^~~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp: In function 'void checkWebServiceAvailability()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:200:60: error: 'TFT_GREEN' was not declared in this scope
  200 |             sendDisplayMessage("Web Service", "Connected", TFT_GREEN, 3000);
      |                                                            ^~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:200:13: error: 'sendDisplayMessage' was not declared in this scope; did you mean 'DisplayMessage_t'?
  200 |             sendDisplayMessage("Web Service", "Connected", TFT_GREEN, 3000);
      |             ^~~~~~~~~~~~~~~~~~
      |             DisplayMessage_t
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:202:63: error: 'TFT_YELLOW' was not declared in this scope
  202 |             sendDisplayMessage("Web Service", "Disconnected", TFT_YELLOW, 3000);
      |                                                               ^~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:202:13: error: 'sendDisplayMessage' was not declared in this scope; did you mean 'DisplayMessage_t'?
  202 |             sendDisplayMessage("Web Service", "Disconnected", TFT_YELLOW, 3000);
      |             ^~~~~~~~~~~~~~~~~~
      |             DisplayMessage_t
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp: In function 'bool testWebServiceConnection()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:219:19: error: no matching function for call to 'HTTPClient::begin(EthernetClient&, char [100])'
  219 |         http.begin(ethClient, web_service_url);
      |         ~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~
In file included from /home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.h:6:
/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src/HTTPClient.h:182:8: note: candidate: 'bool HTTPClient::begin(NetworkClient&, String)'
  182 |   bool begin(NetworkClient &client, String url);
      |        ^~~~~
/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src/HTTPClient.h:182:29: note:   no known conversion for argument 1 from 'EthernetClient' to 'NetworkClient&'
  182 |   bool begin(NetworkClient &client, String url);
      |              ~~~~~~~~~~~~~~~^~~~~~
/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src/HTTPClient.h:183:8: note: candidate: 'bool HTTPClient::begin(NetworkClient&, String, uint16_t, String, bool)'
  183 |   bool begin(NetworkClient &client, String host, uint16_t port, String uri = "/", bool https = false);
      |        ^~~~~
/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src/HTTPClient.h:183:8: note:   candidate expects 5 arguments, 2 provided
/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src/HTTPClient.h:186:8: note: candidate: 'bool HTTPClient::begin(String)'
  186 |   bool begin(String url);
      |        ^~~~~
/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src/HTTPClient.h:186:8: note:   candidate expects 1 argument, 2 provided
/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src/HTTPClient.h:187:8: note: candidate: 'bool HTTPClient::begin(String, uint16_t, String)'
  187 |   bool begin(String host, uint16_t port, String uri = "/");
      |        ^~~~~
/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src/HTTPClient.h:187:21: note:   no known conversion for argument 1 from 'EthernetClient' to 'String'
  187 |   bool begin(String host, uint16_t port, String uri = "/");
      |              ~~~~~~~^~~~
/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src/HTTPClient.h:189:8: note: candidate: 'bool HTTPClient::begin(String, const char*)'
  189 |   bool begin(String url, const char *CAcert);
      |        ^~~~~
/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src/HTTPClient.h:189:21: note:   no known conversion for argument 1 from 'EthernetClient' to 'String'
  189 |   bool begin(String url, const char *CAcert);
      |              ~~~~~~~^~~
/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src/HTTPClient.h:190:8: note: candidate: 'bool HTTPClient::begin(String, uint16_t, String, const char*)'
  190 |   bool begin(String host, uint16_t port, String uri, const char *CAcert);
      |        ^~~~~
/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src/HTTPClient.h:190:8: note:   candidate expects 4 arguments, 2 provided
/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src/HTTPClient.h:191:8: note: candidate: 'bool HTTPClient::begin(String, uint16_t, String, const char*, const char*, const char*)'
  191 |   bool begin(String host, uint16_t port, String uri, const char *CAcert, const char *cli_cert, const char *cli_key);
      |        ^~~~~
/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src/HTTPClient.h:191:8: note:   candidate expects 6 arguments, 2 provided
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp: In function 'void handleConfigurationMode()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:259:28: error: 'checkConfigModeFlag' was not declared in this scope
  259 |     bool should_activate = checkConfigModeFlag();
      |                            ^~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:266:56: error: 'TFT_BLUE' was not declared in this scope
  266 |         sendDisplayMessage("Config Mode", "2 minutes", TFT_BLUE, 0);
      |                                                        ^~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:266:9: error: 'sendDisplayMessage' was not declared in this scope; did you mean 'DisplayMessage_t'?
  266 |         sendDisplayMessage("Config Mode", "2 minutes", TFT_BLUE, 0);
      |         ^~~~~~~~~~~~~~~~~~
      |         DisplayMessage_t
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:269:9: error: 'startConfigurationWebServer' was not declared in this scope
  269 |         startConfigurationWebServer();
      |         ^~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:278:68: error: 'TFT_GREEN' was not declared in this scope
  278 |             sendDisplayMessage("Config Complete", "Restarting...", TFT_GREEN, 3000);
      |                                                                    ^~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:278:13: error: 'sendDisplayMessage' was not declared in this scope; did you mean 'DisplayMessage_t'?
  278 |             sendDisplayMessage("Config Complete", "Restarting...", TFT_GREEN, 3000);
      |             ^~~~~~~~~~~~~~~~~~
      |             DisplayMessage_t
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:281:13: error: 'stopConfigurationWebServer' was not declared in this scope
  281 |             stopConfigurationWebServer();
      |             ^~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:284:13: error: 'clearConfigModeFlag' was not declared in this scope
  284 |             clearConfigModeFlag();
      |             ^~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:291:13: error: 'processConfigurationRequests' was not declared in this scope
  291 |             processConfigurationRequests();
      |             ^~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp: In function 'void handleUDPDiscovery()':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:327:9: error: 'sendUDPDiscoveryResponse' was not declared in this scope
  327 |         sendUDPDiscoveryResponse(&udp);
      |         ^~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp: In function 'void sendUDPDiscoveryResponse(WiFiUDP*)':
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:335:31: error: 'getDeviceName' was not declared in this scope; did you mean 'getDeviceID'?
  335 |     response["device_name"] = getDeviceName();
      |                               ^~~~~~~~~~~~~
      |                               getDeviceID
/home/<USER>/Desktop/y1-26.2.24-STAR-WIFI-E_SPLIT_OPTIMISED/29.1.24_STARWIFI_E_ROTATION/main/task_web.cpp:338:36: error: 'getFirmwareVersion' was not declared in this scope
  338 |     response["firmware_version"] = getFirmwareVersion();
      |                                    ^~~~~~~~~~~~~~~~~~
exit status 1

Compilation error: request for member 'begin' in '0', which is of non-class type 'int'
