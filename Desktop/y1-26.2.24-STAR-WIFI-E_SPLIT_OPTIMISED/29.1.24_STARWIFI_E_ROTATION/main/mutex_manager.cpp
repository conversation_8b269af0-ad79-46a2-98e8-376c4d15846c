#include "mutex_manager.h"

// Mutex handles
SemaphoreHandle_t i2c_mutex = NULL;
SemaphoreHandle_t spi_mutex = NULL;
SemaphoreHandle_t tft_mutex = NULL;
SemaphoreHandle_t gpio_mutex = NULL;
SemaphoreHandle_t eeprom_mutex = NULL;
SemaphoreHandle_t card_data_mutex = NULL;

// Queue handles
QueueHandle_t card_data_queue = NULL;
QueueHandle_t display_queue = NULL;
QueueHandle_t gpio_command_queue = NULL;
QueueHandle_t export_data_queue = NULL;

bool initializeMutexes(void) {
    DEBUG_PRINTLN("Initializing mutexes and queues...");
    
    // Create mutexes
    i2c_mutex = xSemaphoreCreateMutex();
    spi_mutex = xSemaphoreCreateMutex();
    tft_mutex = xSemaphoreCreateMutex();
    gpio_mutex = xSemaphoreCreateMutex();
    eeprom_mutex = xSemaphoreCreateMutex();
    card_data_mutex = xSemaphoreCreateMutex();
    
    // Create queues
    card_data_queue = xQueueCreate(5, sizeof(CardData_t));
    display_queue = xQueueCreate(10, sizeof(DisplayMessage_t));
    gpio_command_queue = xQueueCreate(5, sizeof(GPIOCommand_t));
    export_data_queue = xQueueCreate(20, sizeof(CardData_t));
    
    // Validate creation
    if (!i2c_mutex || !spi_mutex || !tft_mutex || !gpio_mutex || 
        !eeprom_mutex || !card_data_mutex || !card_data_queue || 
        !display_queue || !gpio_command_queue || !export_data_queue) {
        DEBUG_PRINTLN("ERROR: Failed to create mutexes or queues!");
        return false;
    }
    
    DEBUG_PRINTLN("All mutexes and queues created successfully");
    return true;
}

bool takeMutexWithTimeout(SemaphoreHandle_t mutex, const char* mutex_name, uint32_t timeout_ms) {
    if (mutex == NULL) {
        DEBUG_PRINT("ERROR: Null mutex - ");
        DEBUG_PRINTLN(mutex_name);
        return false;
    }
    
    if (xSemaphoreTake(mutex, pdMS_TO_TICKS(timeout_ms)) == pdTRUE) {
        #if DEBUG_MUTEX
        DEBUG_PRINT("Acquired mutex: ");
        DEBUG_PRINTLN(mutex_name);
        #endif
        return true;
    } else {
        DEBUG_PRINT("TIMEOUT: Failed to acquire mutex - ");
        DEBUG_PRINTLN(mutex_name);
        return false;
    }
}

void giveMutexSafe(SemaphoreHandle_t mutex, const char* mutex_name) {
    if (mutex != NULL) {
        xSemaphoreGive(mutex);
        #if DEBUG_MUTEX
        DEBUG_PRINT("Released mutex: ");
        DEBUG_PRINTLN(mutex_name);
        #endif
    }
}