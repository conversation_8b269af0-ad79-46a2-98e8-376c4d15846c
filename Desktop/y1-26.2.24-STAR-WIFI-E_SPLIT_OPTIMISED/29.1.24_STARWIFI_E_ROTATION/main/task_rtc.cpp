#include "task_rtc.h"
#include "mutex_manager.h"

// Global time variables
uint32_t EPOCH_TIME = 0;
static uint32_t last_sync_time = 0;
static const uint32_t SYNC_INTERVAL_MS = 60000; // 1 minute

void TaskRTC(void *pvParameters) {
    (void) pvParameters;
    
    DEBUG_PRINTLN("RTC Task started on Core 1");
    
    // Initialize RTC hardware
    if (!initializeRTC()) {
        DEBUG_PRINTLN("ERROR: RTC initialization failed!");
        vTaskDelete(NULL);
        return;
    }
    
    TickType_t xLastWakeTime = xTaskGetTickCount();
    const TickType_t xFrequency = pdMS_TO_TICKS(1000); // 1 second
    
    for(;;) {
        // Update local time from ESP32 RTC
        updateLocalTime();
        
        // Sync with external RTC periodically
        if ((millis() - last_sync_time) >= SYNC_INTERVAL_MS) {
            syncWithExternalRTC();
            last_sync_time = millis();
        }
        
        // Wait for next cycle
        vTaskDelayUntil(&xLastWakeTime, xFrequency);
    }
}

bool initializeRTC(void) {
    if (!takeMutexWithTimeout(i2c_mutex, "RTC_INIT", I2C_TIMEOUT_MS)) {
        return false;
    }
    
    bool success = false;
    // Initialize RTC hardware here
    // ... RTC initialization code ...
    success = true; // Set based on actual initialization result
    
    giveMutexSafe(i2c_mutex, "RTC_INIT");
    return success;
}

void updateLocalTime(void) {
    // Update ESP32 internal RTC time
    EPOCH_TIME = getEpochTime();
    
    #if DEBUG_TASKS
    static uint32_t debug_counter = 0;
    if (++debug_counter >= 60) { // Print every minute
        DEBUG_PRINT("Current epoch time: ");
        DEBUG_PRINTLN(EPOCH_TIME);
        debug_counter = 0;
    }
    #endif
}

void syncWithExternalRTC(void) {
    if (!takeMutexWithTimeout(i2c_mutex, "RTC_SYNC", I2C_TIMEOUT_MS)) {
        DEBUG_PRINTLN("WARNING: Could not sync with external RTC - I2C busy");
        return;
    }
    
    DEBUG_PRINTLN("Syncing with external RTC...");
    
    // Sync with web service time if available
    if (isWebServiceAvailable()) {
        if (receiveTimeFromDB()) {
            updateExternalRTC();
            DEBUG_PRINTLN("RTC synced with web service");
        }
    }
    
    // Update ESP32 RTC from external RTC
    updateESPRTCFromExternal();
    
    giveMutexSafe(i2c_mutex, "RTC_SYNC");
}