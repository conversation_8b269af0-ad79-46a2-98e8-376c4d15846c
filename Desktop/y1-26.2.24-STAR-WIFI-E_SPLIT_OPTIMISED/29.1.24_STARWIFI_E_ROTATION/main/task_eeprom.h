#ifndef TASK_EEPROM_H
#define TASK_EEPROM_H

#include "system_config.h"
#include "task_definitions.h"
#include "mutex_manager.h"

// EEPROM addresses and constants
#define EMPLOYEE_LUT_START_ADDR 0x1000
#define TRANSACTION_START_ADDR 0x2000
#define MAX_TRANSACTIONS 10000
#define TRANSACTION_RECORD_SIZE 21

// EEPROM function prototypes
void TaskEEPROM(void *pvParameters);
bool initializeEEPROM(void);
bool testEEPROMChip(uint8_t chip_address);
void loadEEPROMState(void);
bool storeTransactionRecord(CardData_t* card_data);
bool writeEEPROMBlock(uint8_t chip_addr, uint32_t address, uint8_t* data, uint16_t length);
bool verifyEEPROMWrite(uint8_t chip_addr, uint32_t address, uint8_t* expected_data, uint16_t length);
bool readEEPROMBlock(uint8_t chip_addr, uint32_t address, uint8_t* buffer, uint16_t length);
void updateTransactionCounters(void);
void saveEEPROMState(void);
bool checkMemoryFull(void);
void handleMemoryFull(CardData_t* card_data);
uint32_t calculateRecordChecksum(uint8_t* data, uint16_t length);

// EEPROM utility functions
uint32_t readUint32FromEEPROM(uint8_t chip_addr, uint32_t address);
bool writeUint32ToEEPROM(uint8_t chip_addr, uint32_t address, uint32_t value);
uint8_t readByteFromEEPROM(uint8_t chip_addr, uint32_t address);
bool writeByteToEEPROM(uint8_t chip_addr, uint32_t address, uint8_t value);
bool readStringFromEEPROM(uint8_t chip_addr, uint32_t address, char* buffer, uint16_t max_length);
bool writeStringToEEPROM(uint8_t chip_addr, uint32_t address, const char* str);
uint32_t getCurrentWriteAddress(void);

#endif