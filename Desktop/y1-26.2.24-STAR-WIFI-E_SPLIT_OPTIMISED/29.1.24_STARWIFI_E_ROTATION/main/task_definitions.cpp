#include "task_definitions.h"

// Task handles
TaskHandle_t rtc_task_handle = NULL;
TaskHandle_t tft_task_handle = NULL;
TaskHandle_t smartcard_task_handle = NULL;
TaskHandle_t carddata_task_handle = NULL;
TaskHandle_t eeprom_task_handle = NULL;
TaskHandle_t gpio_task_handle = NULL;
TaskHandle_t web_task_handle = NULL;
TaskHandle_t export_task_handle = NULL;
TaskHandle_t import_task_handle = NULL;

bool createAllTasks(void) {
    DEBUG_PRINTLN("Creating all tasks...");
    
    BaseType_t result;
    
    // Create Core 1 tasks (hardware interface tasks)
    
    // RTC Task - Core 1, Priority 2
    result = xTaskCreatePinnedToCore(
        TaskRTC,
        "TaskRTC",
        8192,  // Stack size
        NULL,
        PRIORITY_NORMAL,
        &rtc_task_handle,
        1  // Core 1
    );
    if (result != pdPASS) {
        DEBUG_PRINTLN("ERROR: Failed to create RTC task");
        return false;
    }
    
    // TFT Display Task - Core 1, Priority 2
    result = xTaskCreatePinnedToCore(
        TaskTFT,
        "TaskTFT",
        10240,  // Stack size
        NULL,
        PRIORITY_NORMAL,
        &tft_task_handle,
        1  // Core 1
    );
    if (result != pdPASS) {
        DEBUG_PRINTLN("ERROR: Failed to create TFT task");
        return false;
    }
    
    // SmartCard Task - Core 1, Priority 3 (highest for real-time card reading)
    result = xTaskCreatePinnedToCore(
        TaskSmartCard,
        "TaskSmartCard",
        8192,  // Stack size
        NULL,
        PRIORITY_HIGH,
        &smartcard_task_handle,
        1  // Core 1
    );
    if (result != pdPASS) {
        DEBUG_PRINTLN("ERROR: Failed to create SmartCard task");
        return false;
    }
    
    // Card Data Processing Task - Core 1, Priority 2
    result = xTaskCreatePinnedToCore(
        TaskCardData,
        "TaskCardData",
        10240,  // Stack size
        NULL,
        PRIORITY_NORMAL,
        &carddata_task_handle,
        1  // Core 1
    );
    if (result != pdPASS) {
        DEBUG_PRINTLN("ERROR: Failed to create CardData task");
        return false;
    }
    
    // EEPROM Storage Task - Core 1, Priority 2
    result = xTaskCreatePinnedToCore(
        TaskEEPROM,
        "TaskEEPROM",
        8192,  // Stack size
        NULL,
        PRIORITY_NORMAL,
        &eeprom_task_handle,
        1  // Core 1
    );
    if (result != pdPASS) {
        DEBUG_PRINTLN("ERROR: Failed to create EEPROM task");
        return false;
    }
    
    // GPIO Task - Core 1, Priority 2
    result = xTaskCreatePinnedToCore(
        TaskGPIO,
        "TaskGPIO",
        4096,  // Stack size
        NULL,
        PRIORITY_NORMAL,
        &gpio_task_handle,
        1  // Core 1
    );
    if (result != pdPASS) {
        DEBUG_PRINTLN("ERROR: Failed to create GPIO task");
        return false;
    }
    
    // Create Core 0 tasks (network and processing tasks)
    
    // Web Service Task - Core 0, Priority 2
    result = xTaskCreatePinnedToCore(
        TaskWeb,
        "TaskWeb",
        12288,  // Stack size
        NULL,
        PRIORITY_NORMAL,
        &web_task_handle,
        0  // Core 0
    );
    if (result != pdPASS) {
        DEBUG_PRINTLN("ERROR: Failed to create Web task");
        return false;
    }
    
    // Export Task - Core 0, Priority 1
    result = xTaskCreatePinnedToCore(
        TaskExport,
        "TaskExport",
        10240,  // Stack size
        NULL,
        PRIORITY_LOW,
        &export_task_handle,
        0  // Core 0
    );
    if (result != pdPASS) {
        DEBUG_PRINTLN("ERROR: Failed to create Export task");
        return false;
    }
    
    // Import Task - Core 0, Priority 1
    result = xTaskCreatePinnedToCore(
        TaskImport,
        "TaskImport",
        12288,  // Stack size
        NULL,
        PRIORITY_LOW,
        &import_task_handle,
        0  // Core 0
    );
    if (result != pdPASS) {
        DEBUG_PRINTLN("ERROR: Failed to create Import task");
        return false;
    }
    
    DEBUG_PRINTLN("All tasks created successfully");
    
    // Print task information
    DEBUG_PRINTLN("=== Task Summary ===");
    DEBUG_PRINTLN("Core 1 Tasks (Hardware Interface):");
    DEBUG_PRINTLN("  - RTC Task (Priority 2)");
    DEBUG_PRINTLN("  - TFT Display Task (Priority 2)");
    DEBUG_PRINTLN("  - SmartCard Task (Priority 3)");
    DEBUG_PRINTLN("  - CardData Task (Priority 2)");
    DEBUG_PRINTLN("  - EEPROM Task (Priority 2)");
    DEBUG_PRINTLN("  - GPIO Task (Priority 2)");
    DEBUG_PRINTLN("Core 0 Tasks (Network & Processing):");
    DEBUG_PRINTLN("  - Web Service Task (Priority 2)");
    DEBUG_PRINTLN("  - Export Task (Priority 1)");
    DEBUG_PRINTLN("  - Import Task (Priority 1)");
    DEBUG_PRINTLN("==================");
    
    return true;
}