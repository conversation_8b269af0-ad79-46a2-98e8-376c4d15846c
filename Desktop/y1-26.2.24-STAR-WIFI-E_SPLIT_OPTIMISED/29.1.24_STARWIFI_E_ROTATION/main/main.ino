/*
 * ESP32 Multi-Task Employee Card Reader System
 * Version: 29.1.24_STARWIFI_E_ROTATION
 * 
 * System Architecture:
 * - Core 0: Web, Export, Import tasks
 * - Core 1: RTC, TFT, SmartCard, GPIO, EEPROM tasks
 * 
 * Hardware Interfaces:
 * - I2C: RTC, PCF8574 GPIO Expander, EEPROM
 * - SPI: Ethernet, TFT Display
 * - UART: SmartCard Reader
 */

#include "system_config.h"
#include "task_definitions.h"
#include "mutex_manager.h"
#include "hardware_init.h"

void setup() {
    Serial.begin(115200);
    Serial.println("=== ESP32 Card Reader System Starting ===");
    
    // Initialize hardware components
    if (!initializeHardware()) {
        Serial.println("ERROR: Hardware initialization failed!");
        while(1) delay(1000);
    }
    
    // Initialize mutex system
    initializeMutexes();
    
    // Create all tasks
    createAllTasks();
    
    Serial.println("=== System Ready ===");
}

void loop() {
    // Main loop is empty - all work done in tasks
    vTaskDelay(pdMS_TO_TICKS(1000));
}