#ifndef TASK_DISPLAY_H
#define TASK_DISPLAY_H

#include "system_config.h"
#include <TFT_eSPI.h>

// Display message function
void sendDisplayMessage(const char* title, const char* message, uint16_t color, uint32_t duration);

// TFT color constants
#define TFT_RED     0xF800
#define TFT_GREEN   0x07E0
#define TFT_YELLOW  0xFFE0
#define TFT_BLUE    0x001F
#define TFT_WHITE   0xFFFF
#define TFT_BLACK   0x0000

#endif