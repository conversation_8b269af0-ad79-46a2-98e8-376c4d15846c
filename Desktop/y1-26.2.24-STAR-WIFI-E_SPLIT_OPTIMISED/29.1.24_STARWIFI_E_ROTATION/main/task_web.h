#ifndef TASK_WEB_H
#define TASK_WEB_H

#include "system_config.h"
#include <WiFi.h>
#include <HTTPClient.h>
#include <Ethernet.h>
#include <WiFiUdp.h>
#include <ArduinoJson.h>

// Network configuration
#ifndef STASSID
#define STASSID "Print-B1"
#define STAPSK  "p@ssw0rd"
#endif

// Web function prototypes
void TaskWeb(void *pvParameters);
bool initializeNetworking(void);
bool initializeWiFi(void);
bool initializeEthernet(void);
void checkNetworkConnectivity(void);
void handleUDPDiscovery(void);
bool isWebServiceAvailable(void);
void attemptWiFiReconnection(void);
void updateNetworkStatus(void);
bool testWebServiceConnection(void);
void processNetworkRequests(void);

// URL helper functions
String getExportURL(void);
String getEmployeeCountURL(void);
String getEmployeeListURL(void);
String getDeviceConfigURL(void);
String getTimeServerURL(void);
String getAuthHeader(void);
void readStringFromEEPROM(uint8_t chip_addr, uint32_t address, char* buffer, size_t size);
String getDeviceID(void);

// Network status variables
extern bool wifi_connected;
extern bool ethernet_connected;
extern bool webservice_available;

#endif
