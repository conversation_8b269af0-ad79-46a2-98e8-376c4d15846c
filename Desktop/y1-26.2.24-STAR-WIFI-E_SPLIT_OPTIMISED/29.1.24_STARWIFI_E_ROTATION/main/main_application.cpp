#include "main_application.h"
#include "hardware_init.h"
#include "mutex_manager.h"
#include "task_definitions.h"
#include "system_config.h"
#include <EEPROM.h>

// Application state
static bool system_initialized = false;
static uint32_t boot_time = 0;

void setup() {
    // Initialize serial communication
    Serial.begin(115200);
    delay(1000);
    
    DEBUG_PRINTLN("\n=== STAR WIFI E-ROTATION SYSTEM ===");
    DEBUG_PRINTLN("Version: 29.1.24");
    DEBUG_PRINTLN("Booting system...");
    
    boot_time = millis();
    
    // Initialize system components
    if (!initializeSystem()) {
        DEBUG_PRINTLN("FATAL ERROR: System initialization failed");
        DEBUG_PRINTLN("System halted.");
        while(1) {
            delay(1000);
        }
    }
    
    // Create and start all tasks
    if (!createAllTasks()) {
        DEBUG_PRINTLN("FATAL ERROR: Task creation failed");
        DEBUG_PRINTLN("System halted.");
        while(1) {
            delay(1000);
        }
    }
    
    system_initialized = true;
    
    uint32_t boot_duration = millis() - boot_time;
    DEBUG_PRINT("System boot completed in ");
    DEBUG_PRINT(boot_duration);
    DEBUG_PRINTLN(" ms");
    
    DEBUG_PRINTLN("=== SYSTEM READY ===");
    
    // Setup is complete - FreeRTOS scheduler takes over
}

void loop() {
    // Empty - all functionality is handled by FreeRTOS tasks
    // This should never be reached in normal operation
    vTaskDelay(pdMS_TO_TICKS(10000));
}

bool initializeSystem(void) {
    DEBUG_PRINTLN("Initializing system components...");
    
    // Initialize mutexes first
    if (!initializeMutexes()) {
        DEBUG_PRINTLN("ERROR: Mutex initialization failed");
        return false;
    }
    
    // Initialize hardware
    if (!initializeHardware()) {
        DEBUG_PRINTLN("ERROR: Hardware initialization failed");
        return false;
    }
    
    // Initialize internal EEPROM
    if (!initializeInternalEEPROM()) {
        DEBUG_PRINTLN("ERROR: Internal EEPROM initialization failed");
        return false;
    }
    
    // Load system configuration
    if (!loadSystemConfiguration()) {
        DEBUG_PRINTLN("WARNING: Using default system configuration");
    }
    
    // Initialize display queues and buffers
    if (!initializeDisplaySystem()) {
        DEBUG_PRINTLN("ERROR: Display system initialization failed");
        return false;
    }
    
    // Initialize card processing system
    if (!initializeCardSystem()) {
        DEBUG_PRINTLN("ERROR: Card system initialization failed");
        return false;
    }
    
    DEBUG_PRINTLN("System components initialized successfully");
    return true;
}

bool initializeInternalEEPROM(void) {
    DEBUG_PRINTLN("Initializing internal EEPROM...");
    
    EEPROM.begin(INTERNAL_EEPROM_SIZE);
    
    // Check if this is first boot
    uint8_t init_flag = EEPROM.read(100);
    if (init_flag != SET_FLAG) {
        DEBUG_PRINTLN("First boot detected - initializing EEPROM defaults");
        
        // Set default IP length
        EEPROM.write(EEPROM_IP_LEN_ADDRESS, 12);
        EEPROM.write(100, SET_FLAG);
        EEPROM.commit();
        
        DEBUG_PRINTLN("EEPROM defaults set");
    }
    
    return true;
}

bool loadSystemConfiguration(void) {
    DEBUG_PRINTLN("Loading system configuration...");
    
    // Load network configuration
    if (!loadNetworkConfiguration()) {
        DEBUG_PRINTLN("WARNING: Using default network configuration");
    }
    
    // Load device configuration
    if (!loadDeviceConfiguration()) {
        DEBUG_PRINTLN("WARNING: Using default device configuration");
    }
    
    // Load timing configuration
    if (!loadTimingConfiguration()) {
        DEBUG_PRINTLN("WARNING: Using default timing configuration");
    }
    
    return true;
}

bool loadNetworkConfiguration(void) {
    // Load IP configuration from EEPROM
    uint8_t ip_len = EEPROM.read(EEPROM_IP_LEN_ADDRESS);
    if (ip_len > 0 && ip_len < 16) {
        char ip_buffer[16] = {0};
        for (uint8_t i = 0; i < ip_len; i++) {
            ip_buffer[i] = EEPROM.read(EEPROM_IP_ADDRESS + i);
        }
        
        DEBUG_PRINT("Loaded server IP: ");
        DEBUG_PRINTLN(ip_buffer);
        
        // Store in global variables (implementation specific)
        // setServerIP(ip_buffer);
    }
    
    return true;
}

bool loadDeviceConfiguration(void) {
    // Load device-specific settings
    // This would typically load from external EEPROM
    DEBUG_PRINTLN("Device configuration loaded");
    return true;
}

bool loadTimingConfiguration(void) {
    // Load timing parameters
    DEBUG_PRINTLN("Timing configuration loaded");
    return true;
}

bool initializeDisplaySystem(void) {
    DEBUG_PRINTLN("Initializing display system...");
    
    // Create display message queue
    display_queue = xQueueCreate(DISPLAY_QUEUE_SIZE, sizeof(DisplayMessage_t));
    if (display_queue == NULL) {
        DEBUG_PRINTLN("ERROR: Failed to create display queue");
        return false;
    }
    
    // Initialize display buffers
    memset(band3_1, 0, sizeof(band3_1));
    memset(band3_2, 0, sizeof(band3_2));
    band_color = TFT_WHITE;
    
    DEBUG_PRINTLN("Display system initialized");
    return true;
}

bool initializeCardSystem(void) {
    DEBUG_PRINTLN("Initializing card processing system...");
    
    // Create card data queue
    card_queue = xQueueCreate(CARD_QUEUE_SIZE, sizeof(CardData_t));
    if (card_queue == NULL) {
        DEBUG_PRINTLN("ERROR: Failed to create card queue");
        return false;
    }
    
    // Create GPIO command queue
    gpio_queue = xQueueCreate(GPIO_QUEUE_SIZE, sizeof(GPIOCommand_t));
    if (gpio_queue == NULL) {
        DEBUG_PRINTLN("ERROR: Failed to create GPIO queue");
        return false;
    }
    
    // Initialize card detection interrupt
    pinMode(CARD_AVAIL_PIN, INPUT_PULLUP);
    attachInterrupt(digitalPinToInterrupt(CARD_AVAIL_PIN), cardDetectionISR, FALLING);
    
    DEBUG_PRINTLN("Card system initialized");
    return true;
}

void printSystemStatus(void) {
    DEBUG_PRINTLN("\n=== SYSTEM STATUS ===");
    
    // Print uptime
    uint32_t uptime_ms = millis() - boot_time;
    uint32_t uptime_sec = uptime_ms / 1000;
    uint32_t uptime_min = uptime_sec / 60;
    uint32_t uptime_hour = uptime_min / 60;
    
    DEBUG_PRINT("Uptime: ");
    DEBUG_PRINT(uptime_hour);
    DEBUG_PRINT("h ");
    DEBUG_PRINT(uptime_min % 60);
    DEBUG_PRINT("m ");
    DEBUG_PRINT(uptime_sec % 60);
    DEBUG_PRINTLN("s");
    
    // Print memory usage
    DEBUG_PRINT("Free Heap: ");
    DEBUG_PRINT(ESP.getFreeHeap());
    DEBUG_PRINTLN(" bytes");
    
    DEBUG_PRINT("Min Free Heap: ");
    DEBUG_PRINT(ESP.getMinFreeHeap());
    DEBUG_PRINTLN(" bytes");
    
    // Print task information
    DEBUG_PRINTLN("Task Status:");
    DEBUG_PRINT("  RTC Task: ");
    DEBUG_PRINTLN(rtc_task_handle ? "Running" : "Stopped");
    DEBUG_PRINT("  TFT Task: ");
    DEBUG_PRINTLN(tft_task_handle ? "Running" : "Stopped");
    DEBUG_PRINT("  SmartCard Task: ");
    DEBUG_PRINTLN(smartcard_task_handle ? "Running" : "Stopped");
    DEBUG_PRINT("  Export Task: ");
    DEBUG_PRINTLN(export_task_handle ? "Running" : "Stopped");
    DEBUG_PRINT("  Import Task: ");
    DEBUG_PRINTLN(import_task_handle ? "Running" : "Stopped");
    
    // Print network status
    DEBUG_PRINT("WiFi: ");
    DEBUG_PRINTLN(wifi_connected ? "Connected" : "Disconnected");
    DEBUG_PRINT("Ethernet: ");
    DEBUG_PRINTLN(ethernet_connected ? "Connected" : "Disconnected");
    DEBUG_PRINT("Web Service: ");
    DEBUG_PRINTLN(webservice_available ? "Available" : "Unavailable");
    
    DEBUG_PRINTLN("==================");
}

void handleSystemError(const char* error_msg) {
    DEBUG_PRINT("SYSTEM ERROR: ");
    DEBUG_PRINTLN(error_msg);
    
    // Send error to display
    sendDisplayMessage("SYSTEM ERROR", error_msg, TFT_RED, 10000);
    
    // Log error (implementation specific)
    // logSystemError(error_msg);
}

void performSystemReset(void) {
    DEBUG_PRINTLN("Performing system reset...");
    
    // Send reset message to display
    sendDisplayMessage("SYSTEM RESET", "Restarting...", TFT_YELLOW, 3000);
    
    delay(3000);
    ESP.restart();
}

bool isSystemInitialized(void) {
    return system_initialized;
}

uint32_t getSystemUptime(void) {
    return millis() - boot_time;
}
