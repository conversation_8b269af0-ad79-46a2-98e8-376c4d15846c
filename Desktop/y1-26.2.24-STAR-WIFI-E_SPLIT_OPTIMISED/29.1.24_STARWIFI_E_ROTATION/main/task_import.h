#ifndef TASK_IMPORT_H
#define TASK_IMPORT_H

#include "system_config.h"
#include "task_definitions.h"
#include <HTTPClient.h>
#include <ArduinoJson.h>

// Import function prototypes
void TaskImport(void *pvParameters);
void checkLUTImportRequired(void);
bool shouldUpdateLUT(void);
bool importEmployeeLUT(void);
uint32_t getEmployeeCountFromServer(void);
bool importEmployeeBatch(uint32_t start_index, uint32_t batch_size, uint32_t* imported_count);
bool parseAndStoreEmployeeBatch(const String& json_response, uint32_t* imported_count);
bool storeEmployeeInEEPROM(EmployeeRecord_t* employee, uint32_t index);
bool updateLUTMetadata(uint32_t employee_count);
bool clearEmployeeLUTInEEPROM(void);
void importDeviceConfiguration(void);
bool parseAndApplyDeviceConfig(const String& json_response);
void syncTimeWithServer(void);
bool isImportInProgress(void);

// Configuration functions
uint32_t getDisplayDuration(void);
void setDisplayDuration(uint32_t duration);
uint32_t getBeepDuration(void);
void setBeepDuration(uint32_t duration);
const char* getDeviceName(void);
void setDeviceName(const char* name);
void saveDeviceConfiguration(void);

#endif