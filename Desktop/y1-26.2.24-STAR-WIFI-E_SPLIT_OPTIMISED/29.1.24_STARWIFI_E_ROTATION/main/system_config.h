#ifndef SYSTEM_CONFIG_H
#define SYSTEM_CONFIG_H

#include <Wire.h>
#include <WiFi.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <freertos/semphr.h>
#include <freertos/queue.h>
#include "PCF8574.h"

// ESP32-S3 compatibility defines
#ifndef VSPI
#define VSPI SPI
#endif

// Debug configuration
#define DEBUG_ENABLED 1
#define DEBUG_TASKS 1
#define DEBUG_MUTEX 1

#if DEBUG_ENABLED
    #define DEBUG_PRINT(...) Serial.print(__VA_ARGS__)
    #define DEBUG_PRINTLN(...) Serial.println(__VA_ARGS__)
#else
    #define DEBUG_PRINT(...)
    #define DEBUG_PRINTLN(...)
#endif

// Hardware pin definitions
#define CARD_AVAIL_PIN 27
#define CARD_READ_PIN 26
#define CS_PIN 5

// PCF8574 GPIO definitions
#define PCF8574_ADDRESS 0x20
#define OK_LED P0
#define ERR_LED P1
#define BEEP P2
#define RELAY P3
#define SWITCH P4
#define EXT_INPUT P6
#define RELAY2 P7

// Task priorities
#define PRIORITY_HIGH 3
#define PRIORITY_NORMAL 2
#define PRIORITY_LOW 1

// Timeout definitions (in milliseconds)
#define MUTEX_TIMEOUT_MS 1000
#define I2C_TIMEOUT_MS 500
#define SPI_TIMEOUT_MS 1000
#define CARD_READ_TIMEOUT_MS 5000
#define WEB_REQUEST_TIMEOUT_MS 10000

// Buffer sizes
#define MAX_EMPCODE_SIZE 12
#define MAX_RECORD_SIZE 17
#define CARD_DATA_SIZE 57
#define DISPLAY_BUFFER_SIZE 100

// Queue sizes
#define DISPLAY_QUEUE_SIZE 10
#define CARD_QUEUE_SIZE 5
#define GPIO_QUEUE_SIZE 10

// System constants
#define SET_FLAG 0xAA
#define DISPLAY_DURATION_DEFAULT 3000
#define BEEP_DURATION_DEFAULT 400

// Time and EEPROM definitions
#define DEFAULT_EPOCH_TIME 1640995200  // Example epoch time
#define TRANSACTION_START_ADDR 0x1000
#define EMPLOYEE_LUT_START_ADDR 0x2000
#define EEPROM_CHIP1_ADDR_H 0x50
#define EEPROM_CHIP2_ADDR_H 0x51
#define TRANSACTION_RECORD_SIZE 21
#define EMPLOYEE_BATCH_SIZE 50

// WiFi credentials (should be configured via web interface)
#ifndef STASSID
#define STASSID "YourWiFiSSID"
#endif
#ifndef STAPSK
#define STAPSK "YourWiFiPassword"
#endif

// Global variables
extern bool wifi_connected;
extern bool ethernet_connected;
extern bool webservice_available;
extern uint8_t mac_address[6];

#endif
