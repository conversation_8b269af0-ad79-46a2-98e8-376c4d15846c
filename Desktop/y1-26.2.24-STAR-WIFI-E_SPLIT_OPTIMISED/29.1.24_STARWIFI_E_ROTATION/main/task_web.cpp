#include "task_web.h"
#include "mutex_manager.h"

// Web service configuration
static char web_service_url[100] = {0};
static char auth_username[50] = "admin";
static char auth_password[50] = "password";
static bool web_service_available = false;
static uint32_t last_connectivity_check = 0;
static uint32_t connectivity_check_interval = 30000; // 30 seconds

// Network status
static bool wifi_connected = false;
static bool ethernet_connected = false;

void TaskWeb(void *pvParameters) {
    (void) pvParameters;
    
    DEBUG_PRINTLN("Web Task started on Core 0");
    
    // Initialize network connections
    if (!initializeNetworking()) {
        DEBUG_PRINTLN("ERROR: Network initialization failed!");
        vTaskDelete(NULL);
        return;
    }
    
    TickType_t xLastWakeTime = xTaskGetTickCount();
    const TickType_t xFrequency = pdMS_TO_TICKS(5000); // 5 second cycle
    
    for(;;) {
        // Check network connectivity
        checkNetworkConnectivity();
        
        // Check web service availability
        checkWebServiceAvailability();
        
        // Handle configuration requests
        handleConfigurationMode();
        
        // Process UDP discovery requests
        handleUDPDiscovery();
        
        // Update network status display
        updateNetworkStatusDisplay();
        
        vTaskDelayUntil(&xLastWakeTime, xFrequency);
    }
}

bool initializeNetworking(void) {
    DEBUG_PRINTLN("Initializing networking...");
    
    // Initialize WiFi
    if (!initializeWiFi()) {
        DEBUG_PRINTLN("WARNING: WiFi initialization failed");
    }
    
    // Initialize Ethernet
    if (!initializeEthernet()) {
        DEBUG_PRINTLN("WARNING: Ethernet initialization failed");
    }
    
    // Load web service configuration
    loadWebServiceConfig();
    
    return (wifi_connected || ethernet_connected);
}

bool initializeWiFi(void) {
    DEBUG_PRINTLN("Initializing WiFi...");
    
    WiFi.mode(WIFI_STA);
    WiFi.begin(STASSID, STAPSK);
    
    // Wait for connection with timeout
    uint32_t start_time = millis();
    const uint32_t timeout_ms = 20000; // 20 seconds
    
    while (WiFi.status() != WL_CONNECTED && (millis() - start_time) < timeout_ms) {
        vTaskDelay(pdMS_TO_TICKS(500));
        DEBUG_PRINT(".");
    }
    
    if (WiFi.status() == WL_CONNECTED) {
        wifi_connected = true;
        DEBUG_PRINTLN();
        DEBUG_PRINT("WiFi connected! IP: ");
        DEBUG_PRINTLN(WiFi.localIP());
        return true;
    } else {
        DEBUG_PRINTLN();
        DEBUG_PRINTLN("WiFi connection failed");
        return false;
    }
}

bool initializeEthernet(void) {
    if (!takeMutexWithTimeout(spi_mutex, "ETH_INIT", SPI_TIMEOUT_MS)) {
        return false;
    }
    
    DEBUG_PRINTLN("Initializing Ethernet...");
    
    bool success = false;
    
    // Initialize Ethernet with DHCP
    if (Ethernet.begin(mac_address) == 1) {
        ethernet_connected = true;
        success = true;
        
        DEBUG_PRINT("Ethernet connected! IP: ");
        DEBUG_PRINTLN(Ethernet.localIP());
    } else {
        DEBUG_PRINTLN("Ethernet connection failed");
    }
    
    giveMutexSafe(spi_mutex, "ETH_INIT");
    return success;
}

void checkNetworkConnectivity(void) {
    // Check WiFi status
    bool wifi_status = (WiFi.status() == WL_CONNECTED);
    if (wifi_status != wifi_connected) {
        wifi_connected = wifi_status;
        DEBUG_PRINT("WiFi status changed: ");
        DEBUG_PRINTLN(wifi_connected ? "Connected" : "Disconnected");
        
        if (!wifi_connected) {
            // Attempt to reconnect
            attemptWiFiReconnection();
        }
    }
    
    // Check Ethernet status
    bool eth_status = (Ethernet.linkStatus() == LinkON);
    if (eth_status != ethernet_connected) {
        ethernet_connected = eth_status;
        DEBUG_PRINT("Ethernet status changed: ");
        DEBUG_PRINTLN(ethernet_connected ? "Connected" : "Disconnected");
    }
}

void attemptWiFiReconnection(void) {
    static uint32_t last_reconnect_attempt = 0;
    const uint32_t reconnect_interval = 60000; // 1 minute
    
    if ((millis() - last_reconnect_attempt) < reconnect_interval) {
        return; // Too soon to retry
    }
    
    last_reconnect_attempt = millis();
    DEBUG_PRINTLN("Attempting WiFi reconnection...");
    
    WiFi.disconnect();
    vTaskDelay(pdMS_TO_TICKS(1000));
    WiFi.begin(STASSID, STAPSK);
    
    // Wait for connection with shorter timeout
    uint32_t start_time = millis();
    const uint32_t timeout_ms = 10000; // 10 seconds
    
    while (WiFi.status() != WL_CONNECTED && (millis() - start_time) < timeout_ms) {
        vTaskDelay(pdMS_TO_TICKS(500));
    }
    
    if (WiFi.status() == WL_CONNECTED) {
        wifi_connected = true;
        DEBUG_PRINTLN("WiFi reconnection successful");
    } else {
        DEBUG_PRINTLN("WiFi reconnection failed");
    }
}

void checkWebServiceAvailability(void) {
    if ((millis() - last_connectivity_check) < connectivity_check_interval) {
        return; // Too soon to check again
    }
    
    last_connectivity_check = millis();
    
    if (!wifi_connected && !ethernet_connected) {
        web_service_available = false;
        return;
    }
    
    DEBUG_PRINTLN("Checking web service availability...");
    
    // Test web service connectivity
    bool service_available = testWebServiceConnection();
    
    if (service_available != web_service_available) {
        web_service_available = service_available;
        DEBUG_PRINT("Web service status changed: ");
        DEBUG_PRINTLN(web_service_available ? "Available" : "Unavailable");
        
        // Update display
        if (web_service_available) {
            sendDisplayMessage("Web Service", "Connected", TFT_GREEN, 3000);
        } else {
            sendDisplayMessage("Web Service", "Disconnected", TFT_YELLOW, 3000);
        }
    }
}

bool testWebServiceConnection(void) {
    if (strlen(web_service_url) == 0) {
        DEBUG_PRINTLN("Web service URL not configured");
        return false;
    }
    
    HTTPClient http;
    bool success = false;
    
    // Use appropriate client based on available connection
    if (ethernet_connected) {
        EthernetClient ethClient;
        http.begin(ethClient, web_service_url);
    } else if (wifi_connected) {
        http.begin(web_service_url);
    } else {
        return false;
    }
    
    // Set timeout
    http.setTimeout(WEB_REQUEST_TIMEOUT_MS);
    
    // Set authentication
    http.setAuthorization(auth_username, auth_password);
    
    // Send test request
    int httpCode = http.GET();
    
    if (httpCode > 0) {
        if (httpCode == HTTP_CODE_OK || httpCode == HTTP_CODE_UNAUTHORIZED) {
            success = true; // Service is responding
            DEBUG_PRINT("Web service test: HTTP ");
            DEBUG_PRINTLN(httpCode);
        } else {
            DEBUG_PRINT("Web service test failed: HTTP ");
            DEBUG_PRINTLN(httpCode);
        }
    } else {
        DEBUG_PRINT("Web service test error: ");
        DEBUG_PRINTLN(http.errorToString(httpCode));
    }
    
    http.end();
    return success;
}

void handleConfigurationMode(void) {
    static bool config_mode_active = false;
    static uint32_t config_mode_start_time = 0;
    const uint32_t config_mode_duration = 120000; // 2 minutes
    
    // Check if configuration mode should be activated
    bool should_activate = checkConfigModeFlag();
    
    if (should_activate && !config_mode_active) {
        config_mode_active = true;
        config_mode_start_time = millis();
        
        DEBUG_PRINTLN("Entering configuration mode");
        sendDisplayMessage("Config Mode", "2 minutes", TFT_BLUE, 0);
        
        // Start web server for configuration
        startConfigurationWebServer();
    }
    
    // Check if configuration mode should be deactivated
    if (config_mode_active) {
        if ((millis() - config_mode_start_time) >= config_mode_duration) {
            config_mode_active = false;
            
            DEBUG_PRINTLN("Exiting configuration mode");
            sendDisplayMessage("Config Complete", "Restarting...", TFT_GREEN, 3000);
            
            // Stop web server
            stopConfigurationWebServer();
            
            // Clear config mode flag
            clearConfigModeFlag();
            
            // Restart system
            vTaskDelay(pdMS_TO_TICKS(3000));
            ESP.restart();
        } else {
            // Process configuration web requests
            processConfigurationRequests();
        }
    }
}

void handleUDPDiscovery(void) {
    static WiFiUDP udp;
    static bool udp_initialized = false;
    
    if (!wifi_connected && !ethernet_connected) {
        return;
    }
    
    if (!udp_initialized) {
        if (udp.begin(8888)) { // Discovery port
            udp_initialized = true;
            DEBUG_PRINTLN("UDP discovery service started on port 8888");
        } else {
            DEBUG_PRINTLN("Failed to start UDP discovery service");
            return;
        }
    }
    
    // Check for discovery packets
    int packet_size = udp.parsePacket();
    if (packet_size > 0) {
        char packet_buffer[255];
        int len = udp.read(packet_buffer, sizeof(packet_buffer) - 1);
        packet_buffer[len] = '\0';
        
        DEBUG_PRINT("UDP discovery request from ");
        DEBUG_PRINT(udp.remoteIP());
        DEBUG_PRINT(": ");
        DEBUG_PRINTLN(packet_buffer);
        
        // Send discovery response
        sendUDPDiscoveryResponse(&udp);
    }
}

void sendUDPDiscoveryResponse(WiFiUDP* udp) {
    StaticJsonDocument<200> response;
    
    response["device_type"] = "card_reader";
    response["device_name"] = getDeviceName();
    response["ip_address"] = wifi_connected ? WiFi.localIP().toString() : Ethernet.localIP().toString();
    response["mac_address"] = WiFi.macAddress();
    response["firmware_version"] = getFirmwareVersion();
    response["status"] = "online";
    
    String response_str;
    serializeJson(response, response_str);
    
    udp->beginPacket(udp->remoteIP(), udp->remotePort());
    udp->write((const uint8_t*)response_str.c_str(), response_str.length());
    udp->endPacket();
    
    DEBUG_PRINTLN("UDP discovery response sent");
}

bool isWebServiceAvailable(void) {
    return web_service_available;
}

void loadWebServiceConfig(void) {
    // Load configuration from EEPROM
    if (takeMutexWithTimeout(eeprom_mutex, "WEB_CONFIG", 1000)) {
        // Read web service URL
        readStringFromEEPROM(EEPROM_CHIP1_ADDR_H, 0x100, web_service_url, sizeof(web_service_url));
        
        // Read authentication credentials
        readStringFromEEPROM(EEPROM_CHIP1_ADDR_H, 0x200, auth_username, sizeof(auth_username));
        readStringFromEEPROM(EEPROM_CHIP1_ADDR_H, 0x250, auth_password, sizeof(auth_password));
        
        giveMutexSafe(eeprom_mutex, "WEB_CONFIG");
        
        DEBUG_PRINT("Web service URL: ");
        DEBUG_PRINTLN(web_service_url);
        DEBUG_PRINT("Auth username: ");
        DEBUG_PRINTLN(auth_username);
    }
}