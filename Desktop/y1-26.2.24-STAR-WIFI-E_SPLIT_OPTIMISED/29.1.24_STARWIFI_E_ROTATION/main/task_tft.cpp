#include "task_tft.h"
#include "mutex_manager.h"

// TFT display state
static bool tft_initialized = false;
static DisplayMessage_t current_display = {0};
static uint32_t display_clear_time = 0;

// Display bands configuration
typedef struct {
    uint16_t x, y, width, height;
    uint16_t bg_color;
    bool needs_update;
    char content[100];
} DisplayBand_t;

static DisplayBand_t display_bands[5] = {
    {0, 0, 320, 40, TFT_BLUE, true, "System Status"},      // Band 0: Header
    {0, 40, 320, 30, TFT_BLACK, true, ""},                 // Band 1: Time/Date
    {0, 70, 320, 30, TFT_BLACK, true, ""},                 // Band 2: Network Status
    {0, 100, 320, 80, TFT_BLACK, true, ""},                // Band 3: Employee Info
    {0, 180, 320, 60, TFT_BLACK, true, ""}                 // Band 4: System Messages
};

void TaskTFT(void *pvParameters) {
    (void) pvParameters;
    
    DEBUG_PRINTLN("TFT Task started on Core 1");
    
    // Initialize TFT display
    if (!initializeTFT()) {
        DEBUG_PRINTLN("ERROR: TFT initialization failed!");
        vTaskDelete(NULL);
        return;
    }
    
    // Initial display setup
    drawInitialDisplay();
    
    DisplayMessage_t display_msg;
    TickType_t xLastWakeTime = xTaskGetTickCount();
    const TickType_t xFrequency = pdMS_TO_TICKS(100); // 100ms refresh rate
    
    for(;;) {
        // Check for new display messages
        if (xQueueReceive(display_queue, &display_msg, 0) == pdTRUE) {
            processDisplayMessage(&display_msg);
        }
        
        // Update time display
        updateTimeDisplay();
        
        // Update network status
        updateNetworkStatus();
        
        // Handle timed display clearing
        handleTimedDisplay();
        
        // Refresh display if needed
        refreshDisplayBands();
        
        vTaskDelayUntil(&xLastWakeTime, xFrequency);
    }
}

bool initializeTFT(void) {
    if (!takeMutexWithTimeout(spi_mutex, "TFT_INIT", SPI_TIMEOUT_MS)) {
        return false;
    }
    
    bool success = false;
    
    DEBUG_PRINTLN("Initializing TFT display...");
    
    // Initialize TFT hardware
    tft.init();
    tft.setRotation(1); // Landscape mode
    tft.fillScreen(TFT_BLACK);
    tft.setTextColor(TFT_WHITE, TFT_BLACK);
    tft.setTextSize(1);
    
    // Test display
    tft.setCursor(0, 0);
    tft.println("TFT Display Ready");
    
    tft_initialized = true;
    success = true;
    DEBUG_PRINTLN("TFT display initialized successfully");
    
    giveMutexSafe(spi_mutex, "TFT_INIT");
    return success;
}

void drawInitialDisplay(void) {
    if (!takeMutexWithTimeout(tft_mutex, "TFT_INITIAL", SPI_TIMEOUT_MS)) {
        return;
    }
    
    // Clear screen
    tft.fillScreen(TFT_BLACK);
    
    // Draw header band
    tft.fillRect(0, 0, 320, 40, TFT_BLUE);
    tft.setTextColor(TFT_WHITE, TFT_BLUE);
    tft.setCursor(10, 15);
    tft.setTextSize(2);
    tft.println("CARD READER SYSTEM");
    
    // Draw separator lines
    tft.drawLine(0, 40, 320, 40, TFT_WHITE);
    tft.drawLine(0, 70, 320, 70, TFT_GRAY);
    tft.drawLine(0, 100, 320, 100, TFT_GRAY);
    tft.drawLine(0, 180, 320, 180, TFT_GRAY);
    
    // Initialize band contents
    strcpy(display_bands[0].content, "CARD READER SYSTEM");
    strcpy(display_bands[1].content, "Loading...");
    strcpy(display_bands[2].content, "Network: Connecting...");
    strcpy(display_bands[3].content, "Ready for card");
    strcpy(display_bands[4].content, "System Starting...");
    
    giveMutexSafe(tft_mutex, "TFT_INITIAL");
}

void processDisplayMessage(DisplayMessage_t* msg) {
    if (!tft_initialized) return;
    
    DEBUG_PRINT("Processing display message: ");
    DEBUG_PRINTLN(msg->line1);
    
    // Store current message
    memcpy(&current_display, msg, sizeof(DisplayMessage_t));
    
    // Set clear time if duration specified
    if (msg->duration_ms > 0) {
        display_clear_time = millis() + msg->duration_ms;
    } else {
        display_clear_time = 0; // Permanent message
    }
    
    // Update display band 3 (employee info area)
    updateDisplayBand(3, msg->line1, msg->line2, msg->color);
    
    // Trigger GPIO feedback if it's an important message
    if (msg->color == TFT_GREEN) {
        sendGPIOCommand(true, false, true, false, 1000); // OK LED + Beep
    } else if (msg->color == TFT_RED) {
        sendGPIOCommand(false, true, true, false, 2000); // Error LED + Beep
    }
}

void updateDisplayBand(uint8_t band_num, const char* line1, const char* line2, uint16_t color) {
    if (band_num >= 5 || !tft_initialized) return;
    
    if (!takeMutexWithTimeout(tft_mutex, "TFT_BAND", SPI_TIMEOUT_MS)) {
        DEBUG_PRINTLN("WARNING: Could not update display band - TFT busy");
        return;
    }
    
    DisplayBand_t* band = &display_bands[band_num];
    
    // Clear band area
    tft.fillRect(band->x, band->y, band->width, band->height, band->bg_color);
    
    // Set text color
    tft.setTextColor(color, band->bg_color);
    tft.setTextSize(1);
    
    // Draw line 1
    if (line1 && strlen(line1) > 0) {
        tft.setCursor(band->x + 5, band->y + 5);
        tft.println(line1);
    }
    
    // Draw line 2
    if (line2 && strlen(line2) > 0) {
        tft.setCursor(band->x + 5, band->y + 20);
        tft.println(line2);
    }
    
    // Update band content
    snprintf(band->content, sizeof(band->content), "%s %s", 
             line1 ? line1 : "", line2 ? line2 : "");
    band->needs_update = false;
    
    giveMutexSafe(tft_mutex, "TFT_BAND");
}

void updateTimeDisplay(void) {
    static uint32_t last_time_update = 0;
    static char last_time_str[50] = {0};
    
    // Update every second
    if ((millis() - last_time_update) < 1000) return;
    last_time_update = millis();
    
    // Format current time
    char time_str[50];
    formatCurrentTime(time_str, sizeof(time_str));
    
    // Only update if time changed
    if (strcmp(time_str, last_time_str) != 0) {
        strcpy(last_time_str, time_str);
        updateDisplayBand(1, time_str, "", TFT_CYAN);
    }
}

void updateNetworkStatus(void) {
    static uint32_t last_network_update = 0;
    static char last_network_str[50] = {0};
    
    // Update every 5 seconds
    if ((millis() - last_network_update) < 5000) return;
    last_network_update = millis();
    
    char network_str[50];
    uint16_t color = TFT_RED;
    
    if (WiFi.status() == WL_CONNECTED) {
        snprintf(network_str, sizeof(network_str), "WiFi: %s", WiFi.localIP().toString().c_str());
        color = TFT_GREEN;
    } else {
        strcpy(network_str, "WiFi: Disconnected");
        color = TFT_RED;
    }
    
    // Add web service status
    if (isWebServiceAvailable()) {
        strcat(network_str, " | Web: OK");
    } else {
        strcat(network_str, " | Web: Error");
        color = TFT_YELLOW;
    }
    
    // Only update if status changed
    if (strcmp(network_str, last_network_str) != 0) {
        strcpy(last_network_str, network_str);
        updateDisplayBand(2, network_str, "", color);
    }
}

void handleTimedDisplay(void) {
    if (display_clear_time > 0 && millis() >= display_clear_time) {
        // Clear the timed message
        updateDisplayBand(3, "Ready for card", "", TFT_WHITE);
        display_clear_time = 0;
        
        DEBUG_PRINTLN("Cleared timed display message");
    }
}

void refreshDisplayBands(void) {
    // Check if any bands need refreshing
    for (int i = 0; i < 5; i++) {
        if (display_bands[i].needs_update) {
            // Refresh this band
            display_bands[i].needs_update = false;
        }
    }
}

void formatCurrentTime(char* buffer, size_t buffer_size) {
    if (!buffer) return;
    
    // Get current time from RTC
    struct tm timeinfo;
    time_t now = EPOCH_TIME;
    localtime_r(&now, &timeinfo);
    
    strftime(buffer, buffer_size, "%d-%m-%Y %H:%M:%S", &timeinfo);
}

bool sendDisplayMessage(const char* line1, const char* line2, uint16_t color, uint32_t duration_ms) {
    DisplayMessage_t msg = {0};
    
    if (line1) strncpy(msg.line1, line1, sizeof(msg.line1) - 1);
    if (line2) strncpy(msg.line2, line2, sizeof(msg.line2) - 1);
    msg.color = color;
    msg.duration_ms = duration_ms;
    
    if (xQueueSend(display_queue, &msg, pdMS_TO_TICKS(100)) == pdTRUE) {
        return true;
    } else {
        DEBUG_PRINTLN("ERROR: Failed to send display message");
        return false;
    }
}