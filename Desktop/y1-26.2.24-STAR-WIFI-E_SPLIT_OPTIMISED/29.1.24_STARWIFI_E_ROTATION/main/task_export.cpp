#include "task_export.h"
#include "mutex_manager.h"
#include "task_eeprom.h"
#include "task_display.h"

// Export configuration
#define EXPORT_BATCH_SIZE 10
#define EXPORT_RETRY_COUNT 3
#define EXPORT_RETRY_DELAY_MS 5000

// Export state
static uint32_t pending_export_address = TRANSACTION_START_ADDR;
static uint32_t exported_count = 0;
static bool export_in_progress = false;
static bool re_export_requested = false;

void TaskExport(void *pvParameters) {
    (void) pvParameters;
    
    DEBUG_PRINTLN("Export Task started on Core 0");
    
    // Load export state from EEPROM
    loadExportState();
    
    TickType_t xLastWakeTime = xTaskGetTickCount();
    const TickType_t xFrequency = pdMS_TO_TICKS(10000); // 10 second cycle
    
    for(;;) {
        // Check if web service is available
        if (!isWebServiceAvailable()) {
            DEBUG_PRINTLN("Web service unavailable - skipping export cycle");
            vTaskDelayUntil(&xLastWakeTime, xFrequency);
            continue;
        }
        
        // Check for re-export request
        if (re_export_requested) {
            handleReExportRequest();
            re_export_requested = false;
        }
        
        // Check if there are pending transactions to export
        if (hasPendingTransactions()) {
            DEBUG_PRINTLN("Starting export cycle...");
            export_in_progress = true;
            
            // Export batch of transactions
            exportTransactionBatch();
            
            export_in_progress = false;
            DEBUG_PRINTLN("Export cycle completed");
        }
        
        vTaskDelayUntil(&xLastWakeTime, xFrequency);
    }
}

void loadExportState(void) {
    if (!takeMutexWithTimeout(eeprom_mutex, "EXPORT_STATE", 2000)) {
        DEBUG_PRINTLN("WARNING: Could not load export state");
        return;
    }
    
    DEBUG_PRINTLN("Loading export state...");
    
    // Read pending export address
    pending_export_address = readUint32FromEEPROM(EEPROM_CHIP2_ADDR_H, 0x08);
    if (pending_export_address < TRANSACTION_START_ADDR) {
        pending_export_address = TRANSACTION_START_ADDR;
        DEBUG_PRINTLN("Reset pending export address to start");
    }
    
    // Read exported count
    exported_count = readUint32FromEEPROM(EEPROM_CHIP2_ADDR_H, 0x0C);
    
    DEBUG_PRINT("Pending export address: 0x");
    DEBUG_PRINTLN(pending_export_address, HEX);
    DEBUG_PRINT("Exported count: ");
    DEBUG_PRINTLN(exported_count);
    
    giveMutexSafe(eeprom_mutex, "EXPORT_STATE");
}

bool hasPendingTransactions(void) {
    uint32_t current_write_addr = getCurrentWriteAddress();
    return (pending_export_address < current_write_addr);
}

void exportTransactionBatch(void) {
    TransactionRecord_t records[EXPORT_BATCH_SIZE];
    uint8_t records_read = 0;
    
    // Read batch of records from EEPROM
    if (!readTransactionBatch(records, &records_read)) {
        DEBUG_PRINTLN("ERROR: Failed to read transaction batch");
        return;
    }
    
    if (records_read == 0) {
        DEBUG_PRINTLN("No records to export");
        return;
    }
    
    DEBUG_PRINT("Exporting ");
    DEBUG_PRINT(records_read);
    DEBUG_PRINTLN(" records...");
    
    // Send records to web service
    if (sendRecordsToWebService(records, records_read)) {
        // Update export state on successful export
        updateExportState(records_read);
        
        // Update display
        char msg[50];
        snprintf(msg, sizeof(msg), "Exported %d records", records_read);
        sendDisplayMessage("Export Success", msg, TFT_GREEN, 3000);
        
        DEBUG_PRINTLN("Batch export successful");
    } else {
        DEBUG_PRINTLN("ERROR: Batch export failed");
        sendDisplayMessage("Export Failed", "Retrying...", TFT_YELLOW, 3000);
    }
}

bool readTransactionBatch(TransactionRecord_t* records, uint8_t* count) {
    if (!records || !count) return false;
    
    if (!takeMutexWithTimeout(eeprom_mutex, "READ_BATCH", 3000)) {
        DEBUG_PRINTLN("ERROR: Could not acquire EEPROM mutex for batch read");
        return false;
    }
    
    *count = 0;
    uint32_t read_address = pending_export_address;
    uint32_t current_write_addr = getCurrentWriteAddress();
    
    // Read up to EXPORT_BATCH_SIZE records
    while (*count < EXPORT_BATCH_SIZE && read_address < current_write_addr) {
        uint8_t record_buffer[TRANSACTION_RECORD_SIZE];
        
        // Read record from EEPROM
        if (!readEEPROMBlock(EEPROM_CHIP2_ADDR_H, read_address, record_buffer, TRANSACTION_RECORD_SIZE)) {
            DEBUG_PRINT("ERROR: Failed to read record at address 0x");
            DEBUG_PRINTLN(read_address, HEX);
            break;
        }
        
        // Validate record checksum
        if (!validateRecordChecksum(record_buffer)) {
            DEBUG_PRINT("WARNING: Invalid checksum at address 0x");
            DEBUG_PRINTLN(read_address, HEX);
            read_address += TRANSACTION_RECORD_SIZE;
            continue;
        }
        
        // Parse record
        parseTransactionRecord(record_buffer, &records[*count]);
        (*count)++;
        
        read_address += TRANSACTION_RECORD_SIZE;
    }
    
    giveMutexSafe(eeprom_mutex, "READ_BATCH");
    
    DEBUG_PRINT("Read ");
    DEBUG_PRINT(*count);
    DEBUG_PRINTLN(" records from EEPROM");
    
    return (*count > 0);
}

bool validateRecordChecksum(uint8_t* record_buffer) {
    uint32_t calculated_checksum = calculateRecordChecksum(record_buffer, 17);
    uint32_t stored_checksum;
    memcpy(&stored_checksum, &record_buffer[17], 4);
    
    return (calculated_checksum == stored_checksum);
}

void parseTransactionRecord(uint8_t* record_buffer, TransactionRecord_t* record) {
    if (!record_buffer || !record) return;
    
    // Parse employee code
    memcpy(record->empcode, record_buffer, MAX_EMPCODE_SIZE - 1);
    record->empcode[MAX_EMPCODE_SIZE - 1] = '\0';
    
    // Parse timestamp
    memcpy(&record->timestamp, &record_buffer[12], 4);
    
    // Parse I/O flag
    record->inout = record_buffer[16];
    
    // Convert timestamp to readable format
    formatTimestamp(record->timestamp, record->datetime, sizeof(record->datetime));
}

void formatTimestamp(uint32_t timestamp, char* datetime_str, size_t str_size) {
    struct tm timeinfo;
    time_t time_val = timestamp;
    localtime_r(&time_val, &timeinfo);
    
    strftime(datetime_str, str_size, "%d-%m-%Y %H:%M:%S", &timeinfo);
}

bool sendRecordsToWebService(TransactionRecord_t* records, uint8_t count) {
    if (!records || count == 0) return false;
    
    HTTPClient http;
    bool success = false;
    
    // Prepare JSON payload
    StaticJsonDocument<2048> json_doc;
    JsonArray transactions = json_doc.createNestedArray("transactions");
    
    for (uint8_t i = 0; i < count; i++) {
        JsonObject transaction = transactions.createNestedObject();
        transaction["empcode"] = records[i].empcode;
        transaction["datetime"] = records[i].datetime;
        transaction["inout"] = String(records[i].inout);
        transaction["device_id"] = getDeviceID();
    }
    
    String json_payload;
    serializeJson(json_doc, json_payload);
    
    DEBUG_PRINT("JSON payload size: ");
    DEBUG_PRINTLN(json_payload.length());
    
    // Send HTTP request with retries
    for (int retry = 0; retry < EXPORT_RETRY_COUNT; retry++) {
        if (retry > 0) {
            DEBUG_PRINT("Retry attempt ");
            DEBUG_PRINTLN(retry);
            vTaskDelay(pdMS_TO_TICKS(EXPORT_RETRY_DELAY_MS));
        }
        
        // Initialize HTTP client
        if (ethernet_connected) {
            EthernetClient ethClient;
            http.begin(ethClient, getExportURL());
        } else if (wifi_connected) {
            http.begin(getExportURL());
        } else {
            DEBUG_PRINTLN("ERROR: No network connection available");
            break;
        }
        
        // Set headers
        http.addHeader("Content-Type", "application/json");
        http.addHeader("Authorization", getAuthHeader());
        http.setTimeout(WEB_REQUEST_TIMEOUT_MS);
        
        // Send POST request
        int httpCode = http.POST(json_payload);
        
        if (httpCode > 0) {
            DEBUG_PRINT("HTTP Response: ");
            DEBUG_PRINTLN(httpCode);
            
            if (httpCode == HTTP_CODE_OK || httpCode == HTTP_CODE_CREATED) {
                String response = http.getString();
                DEBUG_PRINT("Server response: ");
                DEBUG_PRINTLN(response);
                
                success = true;
                break; // Success, exit retry loop
            } else if (httpCode == HTTP_CODE_UNAUTHORIZED) {
                DEBUG_PRINTLN("ERROR: Authentication failed");
                break; // Don't retry on auth failure
            } else {
                DEBUG_PRINT("ERROR: HTTP error code ");
                DEBUG_PRINTLN(httpCode);
            }
        } else {
            DEBUG_PRINT("ERROR: HTTP request failed - ");
            DEBUG_PRINTLN(http.errorToString(httpCode));
        }
        
        http.end();
    }
    
    return success;
}

void updateExportState(uint8_t exported_records) {
    // Update pending export address
    pending_export_address += (exported_records * TRANSACTION_RECORD_SIZE);
    
    // Update exported count
    exported_count += exported_records;
    
    // Save state to EEPROM
    saveExportState();
    
    DEBUG_PRINT("Updated export state - Address: 0x");
    DEBUG_PRINT(pending_export_address, HEX);
    DEBUG_PRINT(", Count: ");
    DEBUG_PRINTLN(exported_count);
}

void saveExportState(void) {
    if (takeMutexWithTimeout(eeprom_mutex, "SAVE_EXPORT", 1000)) {
        // Save pending export address
        writeUint32ToEEPROM(EEPROM_CHIP2_ADDR_H, 0x08, pending_export_address);
        
        // Save exported count
        writeUint32ToEEPROM(EEPROM_CHIP2_ADDR_H, 0x0C, exported_count);
        
        giveMutexSafe(eeprom_mutex, "SAVE_EXPORT");
    }
}

void handleReExportRequest(void) {
    DEBUG_PRINTLN("Processing re-export request...");
    
    // Reset export state to beginning
    pending_export_address = TRANSACTION_START_ADDR;
    exported_count = 0;
    
    // Save reset state
    saveExportState();
    
    sendDisplayMessage("Re-Export Started", "All Data", TFT_BLUE, 3000);
    DEBUG_PRINTLN("Re-export state reset - will export all data");
}

void setReExportFlag(bool flag) {
    re_export_requested = flag;
}

bool isExportInProgress(void) {
    return export_in_progress;
}