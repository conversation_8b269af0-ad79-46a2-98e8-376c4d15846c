#include "task_gpio.h"
#include "mutex_manager.h"

// PCF8574 instance
static PCF8574 pcf(PCF8574_ADDRESS);
static bool gpio_initialized = false;

void TaskGPIO(void *pvParameters) {
    (void) pvParameters;
    
    DEBUG_PRINTLN("GPIO Task started on Core 1");
    
    // Initialize GPIO expander
    if (!initializeGPIO()) {
        DEBUG_PRINTLN("ERROR: GPIO initialization failed!");
        vTaskDelete(NULL);
        return;
    }
    
    GPIOCommand_t gpio_cmd;
    uint32_t led_off_time = 0;
    uint32_t beep_off_time = 0;
    uint32_t relay_off_time = 0;
    
    for(;;) {
        // Check for new GPIO commands
        if (xQueueReceive(gpio_command_queue, &gpio_cmd, pdMS_TO_TICKS(100)) == pdTRUE) {
            executeGPIOCommand(&gpio_cmd, &led_off_time, &beep_off_time, &relay_off_time);
        }
        
        // Handle timed GPIO operations
        handleTimedGPIO(&led_off_time, &beep_off_time, &relay_off_time);
        
        vTaskDelay(pdMS_TO_TICKS(50));
    }
}

bool initializeGPIO(void) {
    if (!takeMutexWithTimeout(i2c_mutex, "GPIO_INIT", I2C_TIMEOUT_MS)) {
        return false;
    }
    
    bool success = false;
    
    // Initialize PCF8574
    if (pcf.begin()) {
        // Set initial states
        pcf.digitalWrite(OK_LED, HIGH);    // LEDs off (active low)
        pcf.digitalWrite(ERR_LED, HIGH);
        pcf.digitalWrite(BEEP, HIGH);      // Beep off (active low)
        pcf.digitalWrite(RELAY, LOW);      // Relay off
        pcf.digitalWrite(RELAY2, LOW);
        
        // Configure input pin
        pcf.pinMode(EXT_INPUT, INPUT);
        
        gpio_initialized = true;
        success = true;
        DEBUG_PRINTLN("GPIO expander initialized successfully");
    } else {
        DEBUG_PRINTLN("ERROR: Failed to initialize PCF8574");
    }
    
    giveMutexSafe(i2c_mutex, "GPIO_INIT");
    return success;
}

void executeGPIOCommand(GPIOCommand_t* cmd, uint32_t* led_off_time, 
                       uint32_t* beep_off_time, uint32_t* relay_off_time) {
    
    if (!gpio_initialized) {
        DEBUG_PRINTLN("ERROR: GPIO not initialized");
        return;
    }
    
    if (!takeMutexWithTimeout(i2c_mutex, "GPIO_CMD", I2C_TIMEOUT_MS)) {
        DEBUG_PRINTLN("WARNING: Could not execute GPIO command - I2C busy");
        return;
    }
    
    uint32_t current_time = millis();
    
    // Execute LED commands
    if (cmd->ok_led) {
        pcf.digitalWrite(OK_LED, LOW);  // Turn on OK LED
        *led_off_time = current_time + cmd->duration_ms;
        DEBUG_PRINTLN("OK LED turned ON");
    }
    
    if (cmd->err_led) {
        pcf.digitalWrite(ERR_LED, LOW); // Turn on Error LED
        *led_off_time = current_time + cmd->duration_ms;
        DEBUG_PRINTLN("Error LED turned ON");
    }
    
    // Execute beep command
    if (cmd->beep) {
        pcf.digitalWrite(BEEP, LOW);    // Turn on beep
        *beep_off_time = current_time + BEEP_DURATION_DEFAULT;
        DEBUG_PRINTLN("Beep turned ON");
    }
    
    // Execute relay command
    if (cmd->relay) {
        pcf.digitalWrite(RELAY, HIGH);  // Turn on relay
        *relay_off_time = current_time + cmd->duration_ms;
        DEBUG_PRINTLN("Relay turned ON");
    }
    
    giveMutexSafe(i2c_mutex, "GPIO_CMD");
}

void handleTimedGPIO(uint32_t* led_off_time, uint32_t* beep_off_time, uint32_t* relay_off_time) {
    uint32_t current_time = millis();
    bool need_i2c = false;
    
    // Check if any timed operations need to be turned off
    if ((*led_off_time > 0 && current_time >= *led_off_time) ||
        (*beep_off_time > 0 && current_time >= *beep_off_time) ||
        (*relay_off_time > 0 && current_time >= *relay_off_time)) {
        need_i2c = true;
    }
    
    if (!need_i2c) return;
    
    if (!takeMutexWithTimeout(i2c_mutex, "GPIO_TIMER", I2C_TIMEOUT_MS)) {
        return; // Skip this cycle if I2C is busy
    }
    
    // Turn off LEDs if time expired
    if (*led_off_time > 0 && current_time >= *led_off_time) {
        pcf.digitalWrite(OK_LED, HIGH);
        pcf.digitalWrite(ERR_LED, HIGH);
        *led_off_time = 0;
        DEBUG_PRINTLN("LEDs turned OFF");
    }
    
    // Turn off beep if time expired
    if (*beep_off_time > 0 && current_time >= *beep_off_time) {
        pcf.digitalWrite(BEEP, HIGH);
        *beep_off_time = 0;
        DEBUG_PRINTLN("Beep turned OFF");
    }
    
    // Turn off relay if time expired
    if (*relay_off_time > 0 && current_time >= *relay_off_time) {
        pcf.digitalWrite(RELAY, LOW);
        *relay_off_time = 0;
        DEBUG_PRINTLN("Relay turned OFF");
    }
    
    giveMutexSafe(i2c_mutex, "GPIO_TIMER");
}

bool sendGPIOCommand(bool ok_led, bool err_led, bool beep, bool relay, uint32_t duration_ms) {
    GPIOCommand_t cmd = {
        .ok_led = ok_led,
        .err_led = err_led,
        .beep = beep,
        .relay = relay,
        .duration_ms = duration_ms
    };
    
    if (xQueueSend(gpio_command_queue, &cmd, pdMS_TO_TICKS(100)) == pdTRUE) {
        return true;
    } else {
        DEBUG_PRINTLN("ERROR: Failed to send GPIO command");
        return false;
    }
}