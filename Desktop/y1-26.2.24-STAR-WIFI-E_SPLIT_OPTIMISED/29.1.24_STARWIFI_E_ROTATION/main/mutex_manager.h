#ifndef MUTEX_MANAGER_H
#define MUTEX_MANAGER_H

#include "system_config.h"

// Mutex handles for hardware resources
extern SemaphoreHandle_t i2c_mutex;
extern SemaphoreHandle_t spi_mutex;
extern SemaphoreHandle_t tft_mutex;
extern SemaphoreHandle_t gpio_mutex;
extern SemaphoreHandle_t eeprom_mutex;
extern SemaphoreHandle_t card_data_mutex;

// Queue handles for inter-task communication
extern QueueHandle_t card_data_queue;
extern QueueHandle_t display_queue;
extern QueueHandle_t gpio_command_queue;
extern QueueHandle_t export_data_queue;

// Data structures for queues
typedef struct {
    char empcode[MAX_EMPCODE_SIZE];
    char empname[20];
    char inout;
    uint32_t timestamp;
    bool is_valid;
} CardData_t;

typedef struct {
    char line1[50];
    char line2[50];
    uint16_t color;
    uint32_t duration_ms;
} DisplayMessage_t;

typedef struct {
    bool ok_led;
    bool err_led;
    bool beep;
    bool relay;
    uint32_t duration_ms;
} GPIOCommand_t;

// Function prototypes
bool initializeMutexes(void);
bool takeMutexWithTimeout(SemaphoreHandle_t mutex, const char* mutex_name, uint32_t timeout_ms);
void giveMutexSafe(SemaphoreHandle_t mutex, const char* mutex_name);

#endif