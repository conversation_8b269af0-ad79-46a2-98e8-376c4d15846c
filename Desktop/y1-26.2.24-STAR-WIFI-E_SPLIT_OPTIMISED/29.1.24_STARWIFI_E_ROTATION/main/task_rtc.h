#ifndef TASK_RTC_H
#define TASK_RTC_H

#include "system_config.h"

// RTC function prototypes
void TaskRTC(void *pvParameters);
bool initializeRTC(void);
void updateLocalTime(void);
void syncWithExternalRTC(void);
uint32_t getEpochTime(void);
bool receiveTimeFromDB(void);
void updateExternalRTC(void);
void updateESPRTCFromExternal(void);
void updateSystemTime(uint32_t timestamp);
bool isWebServiceAvailable(void);

// Global time variable
extern uint32_t EPOCH_TIME;

#endif