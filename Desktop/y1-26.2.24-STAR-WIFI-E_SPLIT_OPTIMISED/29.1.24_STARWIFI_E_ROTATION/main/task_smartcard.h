#ifndef TASK_SMARTCARD_H
#define TASK_SMARTCARD_H

#include "system_config.h"

// SmartCard function prototypes
void TaskSmartCard(void *pvParameters);
bool initializeCardReader(void);
void IRAM_ATTR cardDetectionISR(void);
void processDetectedCard(void);
bool readCardData(uint8_t* buffer, uint32_t timeout_ms);
bool validateAndParseCard(uint8_t* raw_data, CardData_t* card_data);
void enableCardDetection(void);

// External task handle
extern TaskHandle_t smartcard_task_handle;

#endif